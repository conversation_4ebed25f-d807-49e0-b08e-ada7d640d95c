i want you to create a project where i can search for a person based on any of the name eather in arabic or english , date of birth , gender, and natno


you will use duckdb to look for the data in the table that have the following schema

select * from civil;
┌──────────────────┬─────────────┬─────────┬─────────┬─────────┬─────────┐
│   column_name    │ column_type │  null   │   key   │ default │  extra  │
│     varchar      │   varchar   │ varchar │ varchar │ varchar │ varchar │
├──────────────────┼─────────────┼─────────┼─────────┼─────────┼─────────┤
│ NAT_NO           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ ANAME1           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ ANAME2           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ ANAME3           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ ANAME4           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ BIRTHDT          │ DATE        │ YES     │ NULL    │ NULL    │ NULL    │
│ SEX              │ BIGINT      │ YES     │ NULL    │ NULL    │ NULL    │
│ SOCSTS           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ FNAT_NO          │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ MNAT_NO          │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ DEATH_C          │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ MNATION          │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ CIV_OFF          │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ CIV_NO           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ BIRTH_PL         │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ BGOVR_C          │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ BLIWA_C          │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ BKADA_C          │ BIGINT      │ YES     │ NULL    │ NULL    │ NULL    │
│ BCITY_C          │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ MOTHER_NAME      │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ RESD_CODE        │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ ENAME1           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ ENAME2           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ ENAME3           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ ENAME4           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ newDateofBirth   │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ newDateofBirth_1 │ TIMESTAMP   │ YES     │ NULL    │ NULL    │ NULL    │
│ age              │ BIGINT      │ YES     │ NULL    │ NULL    │ NULL    │
│ governorate      │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ liwa             │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ kada             │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ city             │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ BDate            │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ longitude        │ DOUBLE      │ YES     │ NULL    │ NULL    │ NULL    │
│ latitude         │ DOUBLE      │ YES     │ NULL    │ NULL    │ NULL    │
│ gender           │ BIGINT      │ YES     │ NULL    │ NULL    │ NULL    │
│ gender_desc      │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ is_alive         │ BIGINT      │ YES     │ NULL    │ NULL    │ NULL    │
│ new_BDT          │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ new_BDT_date     │ TIMESTAMP   │ YES     │ NULL    │ NULL    │ NULL    │
│ new_SOCSTS       │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ SOCSTS_Desc      │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
│ zodiac           │ VARCHAR     │ YES     │ NULL    │ NULL    │ NULL    │
├──────────────────┴─────────────┴─────────┴─────────┴─────────┴─────────┤
│ 43 rows                                                      6 columns │



then when i choose the person i want to show me a full family tree till the the  end of related people based on the FNAT_NO and or MNAT_NO


