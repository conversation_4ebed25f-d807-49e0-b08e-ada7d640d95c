# Human Map - Person Search & Family Tree

A TypeScript application for searching people and visualizing family trees using DuckDB.

## Features

- **Person Search**: Search by name (Arabic/English), date of birth, gender, and national number
- **Family Tree Visualization**: Interactive family tree showing relationships through FNAT_NO and MNAT_NO
- **Modern UI**: Clean interface built with Tailwind CSS
- **Real-time Data**: Direct connection to DuckDB civil database

## Database Schema

The application uses a DuckDB database with the `civil` table containing 43 columns including:

- `NAT_NO`: National Number (Primary identifier)
- `ANAME1-4`: Arabic name parts
- `ENAME1-4`: English name parts
- `BIRTHDT`: Birth date
- `SEX`/`gender`: Gender information
- `FNAT_NO`: Father's national number
- `MNAT_NO`: Mother's national number
- Location data (governorate, city, etc.)
- And many more demographic fields

## Installation & Setup

1. **Install dependencies**:

   ```bash
   npm install
   ```

2. **Build the project**:

   ```bash
   npm run build
   ```

3. **Start development server**:

   ```bash
   npm run dev
   ```

4. **Access the application**:
   Open http://localhost:3000 in your browser

## Usage

### Person Search

1. Enter search criteria in any combination:

   - Name (works with Arabic or English)
   - National Number (partial matches supported)
   - Date of Birth
   - Gender (Male/Female)

2. Click "Search" to find matching people
3. Results show person details in cards

### Family Tree

1. Click "View Family Tree" on any person card
2. Interactive tree visualization shows:

   - Selected person (blue node)
   - Parents (green for father, orange for mother)
   - Children and descendants
   - Clickable nodes for more details

3. Use "Expand Tree" to show more generations
4. Click any node to see person details

## API Endpoints

- `GET /api/search` - Search for people
- `GET /api/person/:natNo` - Get person by national number
- `GET /api/family-tree/:natNo` - Get family tree data
- `GET /api/extended-family/:natNo` - Get extended family relationships

## Technology Stack

- **Backend**: TypeScript, Express.js, DuckDB
- **Frontend**: HTML, Tailwind CSS, Vis.js for graphs
- **Database**: DuckDB with civil registry data

## Project Structure

```
├── src/
│   ├── server.ts           # Main Express server
│   ├── duckdb.ts          # Database connection
│   ├── types.ts           # TypeScript interfaces
│   ├── personService.ts   # Person search logic
│   └── familyTreeService.ts # Family tree logic
├── public/
│   ├── index.html         # Main UI
│   └── app.js            # Frontend JavaScript
└── civil.db              # DuckDB database file
```
