import { DuckDBConnection, DuckDBInstance } from "@duckdb/node-api";

let connection: DuckDBConnection;

const initDuckDB = async () => {
  const dbPath = process.env.DUCKDB_PATH || "./eda_fast.db";
  const instance = await DuckDBInstance.create(dbPath);
  connection = await instance.connect();
};

const query = async (sql: string): Promise<any[]> => {
  console.log("--------------------------------");
  console.log(sql);
  console.log("--------------------------------");

  const reader = await connection.run(sql);
  const rows = await reader.getRowObjectsJS();
  return rows.map((row) => {
    const converted = {};
    for (const [key, value] of Object.entries(row)) {
      converted[key] = typeof value === "bigint" ? Number(value) : value;
    }
    return converted;
  });
};

export { initDuckDB, query };
