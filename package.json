{"name": "human-map", "version": "1.0.0", "description": "i want you to create a project where i can search for a person based on any of the name eather in arabic or english , date of birth , gender, and natno", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@duckdb/node-api": "^1.4.0-r.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.5.2", "cors": "^2.8.5", "express": "^5.1.0", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}