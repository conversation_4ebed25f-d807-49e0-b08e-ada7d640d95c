"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FamilyTreeService = void 0;
const personService_1 = require("./personService");
class FamilyTreeService {
    static async buildFamilyTree(rootNatNo, maxDepth = 5) {
        const rootPerson = await personService_1.PersonService.getPersonByNatNo(rootNatNo);
        if (!rootPerson)
            return null;
        const visited = new Set();
        return await this.buildTreeNode(rootPerson, 0, maxDepth, visited);
    }
    static async buildTreeNode(person, currentLevel, maxDepth, visited) {
        // Prevent infinite loops
        if (visited.has(person.NAT_NO)) {
            return {
                person,
                children: [],
                parents: {},
                level: currentLevel
            };
        }
        visited.add(person.NAT_NO);
        const node = {
            person,
            children: [],
            parents: {},
            level: currentLevel
        };
        try {
            // Get parents
            node.parents = await personService_1.PersonService.getParents(person);
            // Get children if we haven't reached max depth
            if (currentLevel < maxDepth) {
                const children = await personService_1.PersonService.getChildren(person.NAT_NO);
                for (const child of children) {
                    if (!visited.has(child.NAT_NO)) {
                        const childNode = await this.buildTreeNode(child, currentLevel + 1, maxDepth, visited);
                        node.children.push(childNode);
                    }
                }
            }
        }
        catch (error) {
            console.error('Error building tree node for person:', person.NAT_NO, error);
        }
        return node;
    }
    static async getExtendedFamily(rootNatNo) {
        const rootPerson = await personService_1.PersonService.getPersonByNatNo(rootNatNo);
        if (!rootPerson) {
            return { ancestors: [], descendants: [], siblings: [] };
        }
        const ancestors = await this.getAncestors(rootPerson);
        const descendants = await this.getDescendants(rootPerson);
        const siblings = await this.getSiblings(rootPerson);
        return { ancestors, descendants, siblings };
    }
    static async getAncestors(person, visited = new Set()) {
        if (visited.has(person.NAT_NO))
            return [];
        visited.add(person.NAT_NO);
        const ancestors = [];
        const parents = await personService_1.PersonService.getParents(person);
        if (parents.father) {
            ancestors.push(parents.father);
            const fatherAncestors = await this.getAncestors(parents.father, visited);
            ancestors.push(...fatherAncestors);
        }
        if (parents.mother) {
            ancestors.push(parents.mother);
            const motherAncestors = await this.getAncestors(parents.mother, visited);
            ancestors.push(...motherAncestors);
        }
        return ancestors;
    }
    static async getDescendants(person, visited = new Set()) {
        if (visited.has(person.NAT_NO))
            return [];
        visited.add(person.NAT_NO);
        const descendants = [];
        const children = await personService_1.PersonService.getChildren(person.NAT_NO);
        for (const child of children) {
            descendants.push(child);
            if (!visited.has(child.NAT_NO)) {
                const childDescendants = await this.getDescendants(child, visited);
                descendants.push(...childDescendants);
            }
        }
        return descendants;
    }
    static async getSiblings(person) {
        const siblings = [];
        if (person.FNAT_NO || person.MNAT_NO) {
            const allChildren = await personService_1.PersonService.getChildren(person.FNAT_NO || '');
            const motherChildren = person.MNAT_NO ? await personService_1.PersonService.getChildren(person.MNAT_NO) : [];
            // Combine and deduplicate
            const allSiblings = [...allChildren, ...motherChildren];
            const uniqueSiblings = allSiblings.filter((sibling, index, arr) => sibling.NAT_NO !== person.NAT_NO &&
                arr.findIndex(s => s.NAT_NO === sibling.NAT_NO) === index);
            siblings.push(...uniqueSiblings);
        }
        return siblings;
    }
}
exports.FamilyTreeService = FamilyTreeService;
//# sourceMappingURL=familyTreeService.js.map