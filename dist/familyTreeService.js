"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FamilyTreeService = void 0;
const personService_1 = require("./personService");
class FamilyTreeService {
    static async buildInteractiveTree(rootNatNo, maxDepth = 2) {
        const allPeople = new Map();
        const relationships = [];
        const visited = new Set();
        await this.expandFromPerson(rootNatNo, maxDepth, allPeople, relationships, visited);
        return {
            nodes: Array.from(allPeople.values()),
            relationships,
        };
    }
    static async expandFromPerson(natNo, levelsToExpand, allPeople, relationships, visited, currentLevel = 0) {
        if (currentLevel >= levelsToExpand || visited.has(natNo))
            return;
        visited.add(natNo);
        const person = await personService_1.PersonService.getPersonByNatNo(natNo);
        if (!person)
            return;
        allPeople.set(person.NAT_NO, person);
        // Add parents
        if (person.FNAT_NO && !allPeople.has(person.FNAT_NO)) {
            const father = await personService_1.PersonService.getPersonByNatNo(person.FNAT_NO);
            if (father) {
                allPeople.set(father.NAT_NO, father);
                relationships.push({
                    from: father.NAT_NO,
                    to: person.NAT_NO,
                    type: "father",
                });
                // Recursively expand from father
                await this.expandFromPerson(father.NAT_NO, levelsToExpand, allPeople, relationships, visited, currentLevel + 1);
            }
        }
        if (person.MNAT_NO && !allPeople.has(person.MNAT_NO)) {
            const mother = await personService_1.PersonService.getPersonByNatNo(person.MNAT_NO);
            if (mother) {
                allPeople.set(mother.NAT_NO, mother);
                relationships.push({
                    from: mother.NAT_NO,
                    to: person.NAT_NO,
                    type: "mother",
                });
                // Recursively expand from mother
                await this.expandFromPerson(mother.NAT_NO, levelsToExpand, allPeople, relationships, visited, currentLevel + 1);
            }
        }
        // Add children
        const children = await personService_1.PersonService.getChildren(person.NAT_NO);
        for (const child of children) {
            if (!allPeople.has(child.NAT_NO)) {
                allPeople.set(child.NAT_NO, child);
                relationships.push({
                    from: person.NAT_NO,
                    to: child.NAT_NO,
                    type: "child",
                });
                // Recursively expand from child
                await this.expandFromPerson(child.NAT_NO, levelsToExpand, allPeople, relationships, visited, currentLevel + 1);
            }
        }
        // Add siblings
        const siblings = await this.getSiblings(person);
        for (const sibling of siblings) {
            if (!allPeople.has(sibling.NAT_NO)) {
                allPeople.set(sibling.NAT_NO, sibling);
                relationships.push({
                    from: person.NAT_NO,
                    to: sibling.NAT_NO,
                    type: "sibling",
                });
                // Don't recursively expand siblings to avoid exponential growth
            }
        }
    }
    static async buildFamilyTree(rootNatNo, maxDepth = 8) {
        const rootPerson = await personService_1.PersonService.getPersonByNatNo(rootNatNo);
        if (!rootPerson)
            return null;
        const visited = new Set();
        return await this.buildMassiveTreeNode(rootPerson, 0, maxDepth, visited);
    }
    static async buildMassiveTreeNode(person, currentLevel, maxDepth, visited) {
        // Prevent infinite loops
        if (visited.has(person.NAT_NO)) {
            return {
                person,
                children: [],
                parents: {},
                level: currentLevel,
            };
        }
        visited.add(person.NAT_NO);
        const node = {
            person,
            children: [],
            parents: {},
            level: currentLevel,
        };
        try {
            // Get parents
            node.parents = await personService_1.PersonService.getParents(person);
            // Get all children (direct descendants)
            if (currentLevel < maxDepth) {
                const children = await personService_1.PersonService.getChildren(person.NAT_NO);
                for (const child of children) {
                    if (!visited.has(child.NAT_NO)) {
                        const childNode = await this.buildMassiveTreeNode(child, currentLevel + 1, maxDepth, visited);
                        node.children.push(childNode);
                    }
                }
            }
            // If this is the root person or close to root, also get siblings and their families
            if (currentLevel <= 2) {
                const siblings = await this.getSiblings(person);
                for (const sibling of siblings) {
                    if (!visited.has(sibling.NAT_NO) && currentLevel < maxDepth - 1) {
                        const siblingNode = await this.buildMassiveTreeNode(sibling, currentLevel, maxDepth, visited);
                        // Add siblings as children of the same parent level conceptually
                        node.children.push(siblingNode);
                    }
                }
            }
            // For ancestors, build their full families too
            if (node.parents.father && currentLevel < maxDepth - 1) {
                const fatherSiblings = await this.getSiblings(node.parents.father);
                for (const uncle of fatherSiblings) {
                    if (!visited.has(uncle.NAT_NO)) {
                        const uncleNode = await this.buildMassiveTreeNode(uncle, currentLevel + 1, maxDepth, visited);
                        node.children.push(uncleNode);
                    }
                }
            }
            if (node.parents.mother && currentLevel < maxDepth - 1) {
                const motherSiblings = await this.getSiblings(node.parents.mother);
                for (const aunt of motherSiblings) {
                    if (!visited.has(aunt.NAT_NO)) {
                        const auntNode = await this.buildMassiveTreeNode(aunt, currentLevel + 1, maxDepth, visited);
                        node.children.push(auntNode);
                    }
                }
            }
        }
        catch (error) {
            console.error("Error building massive tree node for person:", person.NAT_NO, error);
        }
        return node;
    }
    static async buildTreeNode(person, currentLevel, maxDepth, visited) {
        // Prevent infinite loops
        if (visited.has(person.NAT_NO)) {
            return {
                person,
                children: [],
                parents: {},
                level: currentLevel,
            };
        }
        visited.add(person.NAT_NO);
        const node = {
            person,
            children: [],
            parents: {},
            level: currentLevel,
        };
        try {
            // Get parents
            node.parents = await personService_1.PersonService.getParents(person);
            // Get children if we haven't reached max depth
            if (currentLevel < maxDepth) {
                const children = await personService_1.PersonService.getChildren(person.NAT_NO);
                for (const child of children) {
                    if (!visited.has(child.NAT_NO)) {
                        const childNode = await this.buildTreeNode(child, currentLevel + 1, maxDepth, visited);
                        node.children.push(childNode);
                    }
                }
            }
        }
        catch (error) {
            console.error("Error building tree node for person:", person.NAT_NO, error);
        }
        return node;
    }
    static async buildMassiveFamilyNetwork(rootNatNo) {
        const allPeople = new Map();
        const relationships = [];
        const visited = new Set();
        const toProcess = new Set();
        // Start with root person
        toProcess.add(rootNatNo);
        while (toProcess.size > 0 && allPeople.size < 1000) {
            // Limit to prevent infinite loops
            const currentNatNo = toProcess.values().next().value;
            if (!currentNatNo)
                break;
            toProcess.delete(currentNatNo);
            if (visited.has(currentNatNo))
                continue;
            visited.add(currentNatNo);
            const person = await personService_1.PersonService.getPersonByNatNo(currentNatNo);
            if (!person)
                continue;
            allPeople.set(person.NAT_NO, person);
            // Add parents
            if (person.FNAT_NO && !visited.has(person.FNAT_NO)) {
                toProcess.add(person.FNAT_NO);
                relationships.push({
                    from: person.FNAT_NO,
                    to: person.NAT_NO,
                    type: "father",
                });
            }
            if (person.MNAT_NO && !visited.has(person.MNAT_NO)) {
                toProcess.add(person.MNAT_NO);
                relationships.push({
                    from: person.MNAT_NO,
                    to: person.NAT_NO,
                    type: "mother",
                });
            }
            // Add children
            const children = await personService_1.PersonService.getChildren(person.NAT_NO);
            for (const child of children) {
                if (!visited.has(child.NAT_NO)) {
                    toProcess.add(child.NAT_NO);
                    relationships.push({
                        from: person.NAT_NO,
                        to: child.NAT_NO,
                        type: "child",
                    });
                }
            }
            // Add siblings (through parents)
            const siblings = await this.getSiblings(person);
            for (const sibling of siblings) {
                if (!visited.has(sibling.NAT_NO)) {
                    toProcess.add(sibling.NAT_NO);
                    relationships.push({
                        from: person.NAT_NO,
                        to: sibling.NAT_NO,
                        type: "sibling",
                    });
                }
            }
        }
        return {
            nodes: Array.from(allPeople.values()),
            relationships,
        };
    }
    static async getExtendedFamily(rootNatNo) {
        const rootPerson = await personService_1.PersonService.getPersonByNatNo(rootNatNo);
        if (!rootPerson) {
            return { ancestors: [], descendants: [], siblings: [] };
        }
        const ancestors = await this.getAncestors(rootPerson);
        const descendants = await this.getDescendants(rootPerson);
        const siblings = await this.getSiblings(rootPerson);
        return { ancestors, descendants, siblings };
    }
    static async getAncestors(person, visited = new Set()) {
        if (visited.has(person.NAT_NO))
            return [];
        visited.add(person.NAT_NO);
        const ancestors = [];
        const parents = await personService_1.PersonService.getParents(person);
        if (parents.father) {
            ancestors.push(parents.father);
            const fatherAncestors = await this.getAncestors(parents.father, visited);
            ancestors.push(...fatherAncestors);
        }
        if (parents.mother) {
            ancestors.push(parents.mother);
            const motherAncestors = await this.getAncestors(parents.mother, visited);
            ancestors.push(...motherAncestors);
        }
        return ancestors;
    }
    static async getDescendants(person, visited = new Set()) {
        if (visited.has(person.NAT_NO))
            return [];
        visited.add(person.NAT_NO);
        const descendants = [];
        const children = await personService_1.PersonService.getChildren(person.NAT_NO);
        for (const child of children) {
            descendants.push(child);
            if (!visited.has(child.NAT_NO)) {
                const childDescendants = await this.getDescendants(child, visited);
                descendants.push(...childDescendants);
            }
        }
        return descendants;
    }
    static async getSiblings(person) {
        const siblings = [];
        if (person.FNAT_NO || person.MNAT_NO) {
            const allChildren = await personService_1.PersonService.getChildren(person.FNAT_NO || "");
            const motherChildren = person.MNAT_NO
                ? await personService_1.PersonService.getChildren(person.MNAT_NO)
                : [];
            // Combine and deduplicate
            const allSiblings = [...allChildren, ...motherChildren];
            const uniqueSiblings = allSiblings.filter((sibling, index, arr) => sibling.NAT_NO !== person.NAT_NO &&
                arr.findIndex((s) => s.NAT_NO === sibling.NAT_NO) === index);
            siblings.push(...uniqueSiblings);
        }
        return siblings;
    }
}
exports.FamilyTreeService = FamilyTreeService;
//# sourceMappingURL=familyTreeService.js.map