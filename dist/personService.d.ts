import { Person, SearchParams } from "./types";
export declare class PersonService {
    static searchPersons(params: SearchParams): Promise<Person[]>;
    static getPersonByNatNo(natNo: string): Promise<Person | null>;
    static getChildren(natNo: string): Promise<Person[]>;
    static getParents(person: Person): Promise<{
        father?: Person;
        mother?: Person;
    }>;
    static getSiblings(fatherNatNo?: string, motherNatNo?: string): Promise<Person[]>;
}
//# sourceMappingURL=personService.d.ts.map