"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const duckdb_1 = require("./duckdb");
const personService_1 = require("./personService");
const familyTreeService_1 = require("./familyTreeService");
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json());
app.use(express_1.default.static(path_1.default.join(__dirname, "../public")));
// Initialize DuckDB
(0, duckdb_1.initDuckDB)()
    .then(() => {
    console.log("DuckDB initialized successfully");
})
    .catch((error) => {
    console.error("Failed to initialize DuckDB:", error);
    process.exit(1);
});
// API Routes
// Search for persons
app.get("/api/search", async (req, res) => {
    try {
        const searchParams = {
            name: req.query.name,
            dateOfBirth: req.query.dateOfBirth,
            gender: req.query.gender,
            natNo: req.query.natNo,
            limit: req.query.limit ? parseInt(req.query.limit) : 50,
        };
        const results = await personService_1.PersonService.searchPersons(searchParams);
        res.json({
            success: true,
            data: results,
            count: results.length,
        });
    }
    catch (error) {
        console.error("Search error:", error);
        res.status(500).json({
            success: false,
            error: "Failed to search persons",
        });
    }
});
// Get person by national number
app.get("/api/person/:natNo", async (req, res) => {
    try {
        const { natNo } = req.params;
        const person = await personService_1.PersonService.getPersonByNatNo(natNo);
        if (!person) {
            return res.status(404).json({
                success: false,
                error: "Person not found",
            });
        }
        res.json({
            success: true,
            data: person,
        });
    }
    catch (error) {
        console.error("Get person error:", error);
        res.status(500).json({
            success: false,
            error: "Failed to get person",
        });
    }
});
// Get tree level - shows father, mother, and siblings
app.get("/api/tree-level/:natNo", async (req, res) => {
    try {
        const { natNo } = req.params;
        console.log(`--------------------------------\nBuilding tree level for ${natNo}\n--------------------------------`);
        const treeLevel = await familyTreeService_1.FamilyTreeService.buildTreeLevel(natNo);
        res.json({
            success: true,
            data: treeLevel,
        });
    }
    catch (error) {
        console.error("Tree level error:", error);
        res.status(500).json({
            success: false,
            error: "Failed to build tree level",
        });
    }
});
// Expand tree from a specific node
app.post("/api/expand-tree", async (req, res) => {
    try {
        const { natNo, existingNodes, existingRelationships } = req.body;
        console.log(`--------------------------------\nExpanding tree from ${natNo}\n--------------------------------`);
        const expandedTree = await familyTreeService_1.FamilyTreeService.expandTreeFromPerson(natNo, existingNodes, existingRelationships);
        res.json({
            success: true,
            data: expandedTree,
        });
    }
    catch (error) {
        console.error("Expand tree error:", error);
        res.status(500).json({
            success: false,
            error: "Failed to expand tree",
        });
    }
});
// Serve the main HTML page
app.get("/", (req, res) => {
    res.sendFile(path_1.default.join(__dirname, "../public/index.html"));
});
// Start server
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});
exports.default = app;
//# sourceMappingURL=server.js.map