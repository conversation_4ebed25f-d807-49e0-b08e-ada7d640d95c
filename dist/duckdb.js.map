{"version": 3, "file": "duckdb.js", "sourceRoot": "", "sources": ["../src/duckdb.ts"], "names": [], "mappings": ";;;AAAA,+CAAoE;AAEpE,IAAI,UAA4B,CAAC;AAEjC,MAAM,UAAU,GAAG,KAAK,IAAI,EAAE;IAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,YAAY,CAAC;IACvD,MAAM,QAAQ,GAAG,MAAM,yBAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACrD,UAAU,GAAG,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;AACxC,CAAC,CAAC;AAkBO,gCAAU;AAhBnB,MAAM,KAAK,GAAG,KAAK,EAAE,GAAW,EAAkB,EAAE;IAClD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAEhD,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,eAAe,EAAE,CAAC;IAC5C,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QACtB,MAAM,SAAS,GAAQ,EAAE,CAAC;QAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/C,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrE,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEmB,sBAAK"}