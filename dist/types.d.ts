export interface Person {
    NAT_NO: string;
    ANAME1?: string;
    ANAME2?: string;
    ANAME3?: string;
    ANAME4?: string;
    ENAME1?: string;
    ENAME2?: string;
    ENAME3?: string;
    ENAME4?: string;
    BIRTHDT?: string;
    SEX?: number;
    gender?: number;
    gender_desc?: string;
    SOCSTS?: string;
    SOCSTS_Desc?: string;
    FNAT_NO?: string;
    MNAT_NO?: string;
    MOTHER_NAME?: string;
    age?: number;
    governorate?: string;
    liwa?: string;
    kada?: string;
    city?: string;
    longitude?: number;
    latitude?: number;
    is_alive?: number;
    zodiac?: string;
}
export interface SearchParams {
    name?: string;
    dateOfBirth?: string;
    gender?: string;
    natNo?: string;
    limit?: number;
}
export interface FamilyTreeNode {
    person: Person;
    children: FamilyTreeNode[];
    parents: {
        father?: Person;
        mother?: Person;
    };
    level: number;
}
//# sourceMappingURL=types.d.ts.map