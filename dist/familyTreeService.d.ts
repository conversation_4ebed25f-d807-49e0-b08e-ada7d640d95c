import { Person, FamilyTreeNode } from './types';
export declare class FamilyTreeService {
    static buildFamilyTree(rootNatNo: string, maxDepth?: number): Promise<FamilyTreeNode | null>;
    private static buildTreeNode;
    static getExtendedFamily(rootNatNo: string): Promise<{
        ancestors: Person[];
        descendants: Person[];
        siblings: Person[];
    }>;
    private static getAncestors;
    private static getDescendants;
    private static getSiblings;
}
//# sourceMappingURL=familyTreeService.d.ts.map