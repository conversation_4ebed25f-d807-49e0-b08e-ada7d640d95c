import { Person, FamilyTreeNode } from "./types";
export declare class FamilyTreeService {
    static buildFamilyTree(rootNatNo: string, maxDepth?: number): Promise<FamilyTreeNode | null>;
    static buildMassiveTreeNode(person: Person, currentLevel: number, maxDepth: number, visited: Set<string>): Promise<FamilyTreeNode>;
    private static buildTreeNode;
    static buildMassiveFamilyNetwork(rootNatNo: string): Promise<{
        nodes: Person[];
        relationships: Array<{
            from: string;
            to: string;
            type: string;
        }>;
    }>;
    static getExtendedFamily(rootNatNo: string): Promise<{
        ancestors: Person[];
        descendants: Person[];
        siblings: Person[];
    }>;
    private static getAncestors;
    private static getDescendants;
    private static getSiblings;
}
//# sourceMappingURL=familyTreeService.d.ts.map