import { Person } from "./types";
export declare class FamilyTreeService {
    static buildTreeLevel(rootNatNo: string): Promise<{
        nodes: Person[];
        relationships: Array<{
            from: string;
            to: string;
            type: string;
        }>;
        count: number;
    }>;
    static expandTreeFromPerson(natNo: string, existingNodes: Person[], existingRelationships: Array<{
        from: string;
        to: string;
        type: string;
    }>): Promise<{
        nodes: Person[];
        relationships: Array<{
            from: string;
            to: string;
            type: string;
        }>;
        count: number;
    }>;
}
//# sourceMappingURL=familyTreeService.d.ts.map