"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.query = exports.initDuckDB = void 0;
const node_api_1 = require("@duckdb/node-api");
let connection;
const initDuckDB = async () => {
    const dbPath = process.env.DUCKDB_PATH || "./civil.db";
    const instance = await node_api_1.DuckDBInstance.create(dbPath);
    connection = await instance.connect();
};
exports.initDuckDB = initDuckDB;
const query = async (sql) => {
    console.log("--------------------------------");
    console.log(sql);
    console.log("--------------------------------");
    const reader = await connection.run(sql);
    const rows = await reader.getRowObjectsJS();
    return rows.map((row) => {
        const converted = {};
        for (const [key, value] of Object.entries(row)) {
            converted[key] = typeof value === "bigint" ? Number(value) : value;
        }
        return converted;
    });
};
exports.query = query;
//# sourceMappingURL=duckdb.js.map