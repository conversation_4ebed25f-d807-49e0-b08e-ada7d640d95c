import express from 'express';
import cors from 'cors';
import path from 'path';
import { initDuckDB } from './duckdb';
import { PersonService } from './personService';
import { FamilyTreeService } from './familyTreeService';
import { SearchParams } from './types';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// Initialize DuckDB
initDuckDB().then(() => {
  console.log('DuckDB initialized successfully');
}).catch((error) => {
  console.error('Failed to initialize DuckDB:', error);
  process.exit(1);
});

// API Routes

// Search for persons
app.get('/api/search', async (req, res) => {
  try {
    const searchParams: SearchParams = {
      name: req.query.name as string,
      dateOfBirth: req.query.dateOfBirth as string,
      gender: req.query.gender as string,
      natNo: req.query.natNo as string,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 50
    };

    const results = await PersonService.searchPersons(searchParams);
    res.json({
      success: true,
      data: results,
      count: results.length
    });
  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search persons'
    });
  }
});

// Get person by national number
app.get('/api/person/:natNo', async (req, res) => {
  try {
    const { natNo } = req.params;
    const person = await PersonService.getPersonByNatNo(natNo);
    
    if (!person) {
      return res.status(404).json({
        success: false,
        error: 'Person not found'
      });
    }

    res.json({
      success: true,
      data: person
    });
  } catch (error) {
    console.error('Get person error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get person'
    });
  }
});

// Get family tree
app.get('/api/family-tree/:natNo', async (req, res) => {
  try {
    const { natNo } = req.params;
    const maxDepth = req.query.depth ? parseInt(req.query.depth as string) : 3;
    
    const familyTree = await FamilyTreeService.buildFamilyTree(natNo, maxDepth);
    
    if (!familyTree) {
      return res.status(404).json({
        success: false,
        error: 'Person not found'
      });
    }

    res.json({
      success: true,
      data: familyTree
    });
  } catch (error) {
    console.error('Family tree error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to build family tree'
    });
  }
});

// Get extended family
app.get('/api/extended-family/:natNo', async (req, res) => {
  try {
    const { natNo } = req.params;
    const extendedFamily = await FamilyTreeService.getExtendedFamily(natNo);

    res.json({
      success: true,
      data: extendedFamily
    });
  } catch (error) {
    console.error('Extended family error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get extended family'
    });
  }
});

// Serve the main HTML page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});

export default app;
