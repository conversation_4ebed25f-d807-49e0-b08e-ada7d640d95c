import { query } from './duckdb';
import { Person, SearchParams } from './types';

export class PersonService {
  
  static async searchPersons(params: SearchParams): Promise<Person[]> {
    let sql = 'SELECT * FROM civil WHERE 1=1';
    const conditions: string[] = [];
    
    // Search by name (Arabic or English)
    if (params.name && params.name.trim()) {
      const namePattern = `%${params.name.trim()}%`;
      conditions.push(`(
        ANAME1 ILIKE '${namePattern}' OR 
        ANAME2 ILIKE '${namePattern}' OR 
        ANAME3 ILIKE '${namePattern}' OR 
        ANAME4 ILIKE '${namePattern}' OR
        ENAME1 ILIKE '${namePattern}' OR 
        ENAME2 ILIKE '${namePattern}' OR 
        ENAME3 ILIKE '${namePattern}' OR 
        ENAME4 ILIKE '${namePattern}'
      )`);
    }
    
    // Search by national number
    if (params.natNo && params.natNo.trim()) {
      conditions.push(`NAT_NO LIKE '%${params.natNo.trim()}%'`);
    }
    
    // Search by date of birth
    if (params.dateOfBirth && params.dateOfBirth.trim()) {
      conditions.push(`(
        BIRTHDT = '${params.dateOfBirth}' OR 
        new_BDT_date = '${params.dateOfBirth}' OR
        BDate LIKE '%${params.dateOfBirth}%'
      )`);
    }
    
    // Search by gender
    if (params.gender && params.gender.trim()) {
      if (params.gender.toLowerCase() === 'male' || params.gender === '1') {
        conditions.push(`(SEX = 1 OR gender = 1)`);
      } else if (params.gender.toLowerCase() === 'female' || params.gender === '2') {
        conditions.push(`(SEX = 2 OR gender = 2)`);
      }
    }
    
    if (conditions.length > 0) {
      sql += ' AND (' + conditions.join(' AND ') + ')';
    }
    
    // Add limit
    const limit = params.limit || 50;
    sql += ` LIMIT ${limit}`;
    
    try {
      const results = await query(sql);
      return results as Person[];
    } catch (error) {
      console.error('Error searching persons:', error);
      throw new Error('Failed to search persons');
    }
  }
  
  static async getPersonByNatNo(natNo: string): Promise<Person | null> {
    const sql = `SELECT * FROM civil WHERE NAT_NO = '${natNo}' LIMIT 1`;
    
    try {
      const results = await query(sql);
      return results.length > 0 ? results[0] as Person : null;
    } catch (error) {
      console.error('Error getting person by NAT_NO:', error);
      throw new Error('Failed to get person');
    }
  }
  
  static async getChildren(natNo: string): Promise<Person[]> {
    const sql = `SELECT * FROM civil WHERE FNAT_NO = '${natNo}' OR MNAT_NO = '${natNo}'`;
    
    try {
      const results = await query(sql);
      return results as Person[];
    } catch (error) {
      console.error('Error getting children:', error);
      return [];
    }
  }
  
  static async getParents(person: Person): Promise<{ father?: Person; mother?: Person }> {
    const parents: { father?: Person; mother?: Person } = {};
    
    try {
      if (person.FNAT_NO) {
        const father = await this.getPersonByNatNo(person.FNAT_NO);
        if (father) parents.father = father;
      }
      
      if (person.MNAT_NO) {
        const mother = await this.getPersonByNatNo(person.MNAT_NO);
        if (mother) parents.mother = mother;
      }
    } catch (error) {
      console.error('Error getting parents:', error);
    }
    
    return parents;
  }
}
