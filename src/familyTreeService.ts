import { PersonService } from "./personService";
import { Person, FamilyTreeNode } from "./types";

export class FamilyTreeService {
  // Build a simple tree level showing father, mother, and siblings
  static async buildTreeLevel(rootNatNo: string): Promise<{
    nodes: Person[];
    relationships: Array<{ from: string; to: string; type: string }>;
    count: number;
  }> {
    const allPeople = new Map<string, Person>();
    const relationships: Array<{ from: string; to: string; type: string }> = [];

    // Get the root person
    const rootPerson = await PersonService.getPersonByNatNo(rootNatNo);
    if (!rootPerson) {
      throw new Error("Person not found");
    }
    allPeople.set(rootNatNo, rootPerson);

    // Get father
    if (rootPerson.FNAT_NO) {
      const father = await PersonService.getPersonByNatNo(rootPerson.FNAT_NO);
      if (father) {
        allPeople.set(father.NAT_NO, father);
        relationships.push({
          from: father.NAT_NO,
          to: rootNatNo,
          type: "parent-child",
        });
      }
    }

    // Get mother
    if (rootPerson.MNAT_NO) {
      const mother = await PersonService.getPersonByNatNo(rootPerson.MNAT_NO);
      if (mother) {
        allPeople.set(mother.NAT_NO, mother);
        relationships.push({
          from: mother.NAT_NO,
          to: rootNatNo,
          type: "parent-child",
        });
      }
    }

    // Get siblings (people with same parents)
    if (rootPerson.FNAT_NO || rootPerson.MNAT_NO) {
      const siblings = await PersonService.getSiblings(
        rootPerson.FNAT_NO,
        rootPerson.MNAT_NO
      );
      for (const sibling of siblings) {
        if (sibling.NAT_NO !== rootNatNo) {
          allPeople.set(sibling.NAT_NO, sibling);
          // Connect siblings to parents
          if (rootPerson.FNAT_NO && allPeople.has(rootPerson.FNAT_NO)) {
            relationships.push({
              from: rootPerson.FNAT_NO,
              to: sibling.NAT_NO,
              type: "parent-child",
            });
          }
          if (rootPerson.MNAT_NO && allPeople.has(rootPerson.MNAT_NO)) {
            relationships.push({
              from: rootPerson.MNAT_NO,
              to: sibling.NAT_NO,
              type: "parent-child",
            });
          }
        }
      }
    }

    return {
      nodes: Array.from(allPeople.values()),
      relationships,
      count: allPeople.size,
    };
  }

  // Expand tree by adding parents and siblings of a specific person
  static async expandTreeFromPerson(
    natNo: string,
    existingNodes: Person[],
    existingRelationships: Array<{ from: string; to: string; type: string }>
  ): Promise<{
    nodes: Person[];
    relationships: Array<{ from: string; to: string; type: string }>;
    count: number;
  }> {
    const allPeople = new Map<string, Person>();
    const relationships: Array<{ from: string; to: string; type: string }> = [
      ...existingRelationships,
    ];

    // Add existing nodes to map
    for (const person of existingNodes) {
      allPeople.set(person.NAT_NO, person);
    }

    // Get the person to expand from
    const person = await PersonService.getPersonByNatNo(natNo);
    if (!person) {
      return {
        nodes: existingNodes,
        relationships: existingRelationships,
        count: existingNodes.length,
      };
    }

    // Add father if not already present
    if (person.FNAT_NO && !allPeople.has(person.FNAT_NO)) {
      const father = await PersonService.getPersonByNatNo(person.FNAT_NO);
      if (father) {
        allPeople.set(father.NAT_NO, father);
        relationships.push({
          from: father.NAT_NO,
          to: natNo,
          type: "parent-child",
        });
      }
    }

    // Add mother if not already present
    if (person.MNAT_NO && !allPeople.has(person.MNAT_NO)) {
      const mother = await PersonService.getPersonByNatNo(person.MNAT_NO);
      if (mother) {
        allPeople.set(mother.NAT_NO, mother);
        relationships.push({
          from: mother.NAT_NO,
          to: natNo,
          type: "parent-child",
        });
      }
    }

    // Add siblings if not already present
    if (person.FNAT_NO || person.MNAT_NO) {
      const siblings = await PersonService.getSiblings(
        person.FNAT_NO,
        person.MNAT_NO
      );
      for (const sibling of siblings) {
        if (sibling.NAT_NO !== natNo && !allPeople.has(sibling.NAT_NO)) {
          allPeople.set(sibling.NAT_NO, sibling);
          // Connect siblings to parents
          if (person.FNAT_NO && allPeople.has(person.FNAT_NO)) {
            relationships.push({
              from: person.FNAT_NO,
              to: sibling.NAT_NO,
              type: "parent-child",
            });
          }
          if (person.MNAT_NO && allPeople.has(person.MNAT_NO)) {
            relationships.push({
              from: person.MNAT_NO,
              to: sibling.NAT_NO,
              type: "parent-child",
            });
          }
        }
      }
    }

    return {
      nodes: Array.from(allPeople.values()),
      relationships,
      count: allPeople.size,
    };
  }
}
