import { PersonService } from './personService';
import { Person, FamilyTreeNode } from './types';

export class FamilyTreeService {
  
  static async buildFamilyTree(rootNatNo: string, maxDepth: number = 5): Promise<FamilyTreeNode | null> {
    const rootPerson = await PersonService.getPersonByNatNo(rootNatNo);
    if (!rootPerson) return null;
    
    const visited = new Set<string>();
    
    return await this.buildTreeNode(rootPerson, 0, maxDepth, visited);
  }
  
  private static async buildTreeNode(
    person: Person, 
    currentLevel: number, 
    maxDepth: number, 
    visited: Set<string>
  ): Promise<FamilyTreeNode> {
    
    // Prevent infinite loops
    if (visited.has(person.NAT_NO)) {
      return {
        person,
        children: [],
        parents: {},
        level: currentLevel
      };
    }
    
    visited.add(person.NAT_NO);
    
    const node: FamilyTreeNode = {
      person,
      children: [],
      parents: {},
      level: currentLevel
    };
    
    try {
      // Get parents
      node.parents = await PersonService.getParents(person);
      
      // Get children if we haven't reached max depth
      if (currentLevel < maxDepth) {
        const children = await PersonService.getChildren(person.NAT_NO);
        
        for (const child of children) {
          if (!visited.has(child.NAT_NO)) {
            const childNode = await this.buildTreeNode(child, currentLevel + 1, maxDepth, visited);
            node.children.push(childNode);
          }
        }
      }
    } catch (error) {
      console.error('Error building tree node for person:', person.NAT_NO, error);
    }
    
    return node;
  }
  
  static async getExtendedFamily(rootNatNo: string): Promise<{
    ancestors: Person[];
    descendants: Person[];
    siblings: Person[];
  }> {
    const rootPerson = await PersonService.getPersonByNatNo(rootNatNo);
    if (!rootPerson) {
      return { ancestors: [], descendants: [], siblings: [] };
    }
    
    const ancestors = await this.getAncestors(rootPerson);
    const descendants = await this.getDescendants(rootPerson);
    const siblings = await this.getSiblings(rootPerson);
    
    return { ancestors, descendants, siblings };
  }
  
  private static async getAncestors(person: Person, visited: Set<string> = new Set()): Promise<Person[]> {
    if (visited.has(person.NAT_NO)) return [];
    visited.add(person.NAT_NO);
    
    const ancestors: Person[] = [];
    const parents = await PersonService.getParents(person);
    
    if (parents.father) {
      ancestors.push(parents.father);
      const fatherAncestors = await this.getAncestors(parents.father, visited);
      ancestors.push(...fatherAncestors);
    }
    
    if (parents.mother) {
      ancestors.push(parents.mother);
      const motherAncestors = await this.getAncestors(parents.mother, visited);
      ancestors.push(...motherAncestors);
    }
    
    return ancestors;
  }
  
  private static async getDescendants(person: Person, visited: Set<string> = new Set()): Promise<Person[]> {
    if (visited.has(person.NAT_NO)) return [];
    visited.add(person.NAT_NO);
    
    const descendants: Person[] = [];
    const children = await PersonService.getChildren(person.NAT_NO);
    
    for (const child of children) {
      descendants.push(child);
      if (!visited.has(child.NAT_NO)) {
        const childDescendants = await this.getDescendants(child, visited);
        descendants.push(...childDescendants);
      }
    }
    
    return descendants;
  }
  
  private static async getSiblings(person: Person): Promise<Person[]> {
    const siblings: Person[] = [];
    
    if (person.FNAT_NO || person.MNAT_NO) {
      const allChildren = await PersonService.getChildren(person.FNAT_NO || '');
      const motherChildren = person.MNAT_NO ? await PersonService.getChildren(person.MNAT_NO) : [];
      
      // Combine and deduplicate
      const allSiblings = [...allChildren, ...motherChildren];
      const uniqueSiblings = allSiblings.filter((sibling, index, arr) => 
        sibling.NAT_NO !== person.NAT_NO && 
        arr.findIndex(s => s.NAT_NO === sibling.NAT_NO) === index
      );
      
      siblings.push(...uniqueSiblings);
    }
    
    return siblings;
  }
}
