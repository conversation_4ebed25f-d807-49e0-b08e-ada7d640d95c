import { PersonService } from "./personService";
import { Person, FamilyTreeNode } from "./types";

export class FamilyTreeService {
  static async buildFamilyTree(
    rootNatNo: string,
    maxDepth: number = 8
  ): Promise<FamilyTreeNode | null> {
    const rootPerson = await PersonService.getPersonByNatNo(rootNatNo);
    if (!rootPerson) return null;

    const visited = new Set<string>();

    return await this.buildMassiveTreeNode(rootPerson, 0, maxDepth, visited);
  }

  static async buildMassiveTreeNode(
    person: Person,
    currentLevel: number,
    maxDepth: number,
    visited: Set<string>
  ): Promise<FamilyTreeNode> {
    // Prevent infinite loops
    if (visited.has(person.NAT_NO)) {
      return {
        person,
        children: [],
        parents: {},
        level: currentLevel,
      };
    }

    visited.add(person.NAT_NO);

    const node: FamilyTreeNode = {
      person,
      children: [],
      parents: {},
      level: currentLevel,
    };

    try {
      // Get parents
      node.parents = await PersonService.getParents(person);

      // Get all children (direct descendants)
      if (currentLevel < maxDepth) {
        const children = await PersonService.getChildren(person.NAT_NO);

        for (const child of children) {
          if (!visited.has(child.NAT_NO)) {
            const childNode = await this.buildMassiveTreeNode(
              child,
              currentLevel + 1,
              maxDepth,
              visited
            );
            node.children.push(childNode);
          }
        }
      }

      // If this is the root person or close to root, also get siblings and their families
      if (currentLevel <= 2) {
        const siblings = await this.getSiblings(person);
        for (const sibling of siblings) {
          if (!visited.has(sibling.NAT_NO) && currentLevel < maxDepth - 1) {
            const siblingNode = await this.buildMassiveTreeNode(
              sibling,
              currentLevel,
              maxDepth,
              visited
            );
            // Add siblings as children of the same parent level conceptually
            node.children.push(siblingNode);
          }
        }
      }

      // For ancestors, build their full families too
      if (node.parents.father && currentLevel < maxDepth - 1) {
        const fatherSiblings = await this.getSiblings(node.parents.father);
        for (const uncle of fatherSiblings) {
          if (!visited.has(uncle.NAT_NO)) {
            const uncleNode = await this.buildMassiveTreeNode(
              uncle,
              currentLevel + 1,
              maxDepth,
              visited
            );
            node.children.push(uncleNode);
          }
        }
      }

      if (node.parents.mother && currentLevel < maxDepth - 1) {
        const motherSiblings = await this.getSiblings(node.parents.mother);
        for (const aunt of motherSiblings) {
          if (!visited.has(aunt.NAT_NO)) {
            const auntNode = await this.buildMassiveTreeNode(
              aunt,
              currentLevel + 1,
              maxDepth,
              visited
            );
            node.children.push(auntNode);
          }
        }
      }
    } catch (error) {
      console.error(
        "Error building massive tree node for person:",
        person.NAT_NO,
        error
      );
    }

    return node;
  }

  private static async buildTreeNode(
    person: Person,
    currentLevel: number,
    maxDepth: number,
    visited: Set<string>
  ): Promise<FamilyTreeNode> {
    // Prevent infinite loops
    if (visited.has(person.NAT_NO)) {
      return {
        person,
        children: [],
        parents: {},
        level: currentLevel,
      };
    }

    visited.add(person.NAT_NO);

    const node: FamilyTreeNode = {
      person,
      children: [],
      parents: {},
      level: currentLevel,
    };

    try {
      // Get parents
      node.parents = await PersonService.getParents(person);

      // Get children if we haven't reached max depth
      if (currentLevel < maxDepth) {
        const children = await PersonService.getChildren(person.NAT_NO);

        for (const child of children) {
          if (!visited.has(child.NAT_NO)) {
            const childNode = await this.buildTreeNode(
              child,
              currentLevel + 1,
              maxDepth,
              visited
            );
            node.children.push(childNode);
          }
        }
      }
    } catch (error) {
      console.error(
        "Error building tree node for person:",
        person.NAT_NO,
        error
      );
    }

    return node;
  }

  static async buildMassiveFamilyNetwork(rootNatNo: string): Promise<{
    nodes: Person[];
    relationships: Array<{ from: string; to: string; type: string }>;
  }> {
    const allPeople = new Map<string, Person>();
    const relationships: Array<{ from: string; to: string; type: string }> = [];
    const visited = new Set<string>();
    const toProcess = new Set<string>();

    // Start with root person
    toProcess.add(rootNatNo);

    while (toProcess.size > 0 && allPeople.size < 1000) {
      // Limit to prevent infinite loops
      const currentNatNo = toProcess.values().next().value as string;
      if (!currentNatNo) break;

      toProcess.delete(currentNatNo);

      if (visited.has(currentNatNo)) continue;
      visited.add(currentNatNo);

      const person = await PersonService.getPersonByNatNo(currentNatNo);
      if (!person) continue;

      allPeople.set(person.NAT_NO, person);

      // Add parents
      if (person.FNAT_NO && !visited.has(person.FNAT_NO)) {
        toProcess.add(person.FNAT_NO);
        relationships.push({
          from: person.FNAT_NO,
          to: person.NAT_NO,
          type: "father",
        });
      }

      if (person.MNAT_NO && !visited.has(person.MNAT_NO)) {
        toProcess.add(person.MNAT_NO);
        relationships.push({
          from: person.MNAT_NO,
          to: person.NAT_NO,
          type: "mother",
        });
      }

      // Add children
      const children = await PersonService.getChildren(person.NAT_NO);
      for (const child of children) {
        if (!visited.has(child.NAT_NO)) {
          toProcess.add(child.NAT_NO);
          relationships.push({
            from: person.NAT_NO,
            to: child.NAT_NO,
            type: "child",
          });
        }
      }

      // Add siblings (through parents)
      const siblings = await this.getSiblings(person);
      for (const sibling of siblings) {
        if (!visited.has(sibling.NAT_NO)) {
          toProcess.add(sibling.NAT_NO);
          relationships.push({
            from: person.NAT_NO,
            to: sibling.NAT_NO,
            type: "sibling",
          });
        }
      }
    }

    return {
      nodes: Array.from(allPeople.values()),
      relationships,
    };
  }

  static async getExtendedFamily(rootNatNo: string): Promise<{
    ancestors: Person[];
    descendants: Person[];
    siblings: Person[];
  }> {
    const rootPerson = await PersonService.getPersonByNatNo(rootNatNo);
    if (!rootPerson) {
      return { ancestors: [], descendants: [], siblings: [] };
    }

    const ancestors = await this.getAncestors(rootPerson);
    const descendants = await this.getDescendants(rootPerson);
    const siblings = await this.getSiblings(rootPerson);

    return { ancestors, descendants, siblings };
  }

  private static async getAncestors(
    person: Person,
    visited: Set<string> = new Set()
  ): Promise<Person[]> {
    if (visited.has(person.NAT_NO)) return [];
    visited.add(person.NAT_NO);

    const ancestors: Person[] = [];
    const parents = await PersonService.getParents(person);

    if (parents.father) {
      ancestors.push(parents.father);
      const fatherAncestors = await this.getAncestors(parents.father, visited);
      ancestors.push(...fatherAncestors);
    }

    if (parents.mother) {
      ancestors.push(parents.mother);
      const motherAncestors = await this.getAncestors(parents.mother, visited);
      ancestors.push(...motherAncestors);
    }

    return ancestors;
  }

  private static async getDescendants(
    person: Person,
    visited: Set<string> = new Set()
  ): Promise<Person[]> {
    if (visited.has(person.NAT_NO)) return [];
    visited.add(person.NAT_NO);

    const descendants: Person[] = [];
    const children = await PersonService.getChildren(person.NAT_NO);

    for (const child of children) {
      descendants.push(child);
      if (!visited.has(child.NAT_NO)) {
        const childDescendants = await this.getDescendants(child, visited);
        descendants.push(...childDescendants);
      }
    }

    return descendants;
  }

  private static async getSiblings(person: Person): Promise<Person[]> {
    const siblings: Person[] = [];

    if (person.FNAT_NO || person.MNAT_NO) {
      const allChildren = await PersonService.getChildren(person.FNAT_NO || "");
      const motherChildren = person.MNAT_NO
        ? await PersonService.getChildren(person.MNAT_NO)
        : [];

      // Combine and deduplicate
      const allSiblings = [...allChildren, ...motherChildren];
      const uniqueSiblings = allSiblings.filter(
        (sibling, index, arr) =>
          sibling.NAT_NO !== person.NAT_NO &&
          arr.findIndex((s) => s.NAT_NO === sibling.NAT_NO) === index
      );

      siblings.push(...uniqueSiblings);
    }

    return siblings;
  }
}
