{"name": "@duckdb/node-bindings", "version": "1.4.0-r.1", "license": "MIT", "main": "./duckdb.js", "types": "./duckdb.d.ts", "optionalDependencies": {"@duckdb/node-bindings-darwin-arm64": "1.4.0-r.1", "@duckdb/node-bindings-darwin-x64": "1.4.0-r.1", "@duckdb/node-bindings-linux-arm64": "1.4.0-r.1", "@duckdb/node-bindings-linux-x64": "1.4.0-r.1", "@duckdb/node-bindings-win32-x64": "1.4.0-r.1"}, "repository": {"type": "git", "url": "https://github.com/duckdb/duckdb-node-neo.git"}}