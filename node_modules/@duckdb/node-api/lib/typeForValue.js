"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.typeForValue = typeForValue;
const DuckDBType_1 = require("./DuckDBType");
const values_1 = require("./values");
function typeForValue(value) {
    if (value === null) {
        return DuckDBType_1.SQLNULL;
    }
    else {
        switch (typeof value) {
            case 'boolean':
                return DuckDBType_1.BOOLEAN;
            case 'number':
                if (Math.round(value) === value) {
                    return DuckDBType_1.INTEGER;
                }
                else {
                    return DuckDBType_1.DOUBLE;
                }
            case 'bigint':
                return DuckDBType_1.HUGEINT;
            case 'string':
                return DuckDBType_1.VARCHAR;
            case 'object':
                if (value instanceof values_1.DuckDBArrayValue) {
                    return (0, DuckDBType_1.ARRAY)(typeForValue(value.items[0]), value.items.length);
                }
                else if (value instanceof values_1.DuckDBBitValue) {
                    return DuckDBType_1.BIT;
                }
                else if (value instanceof values_1.DuckDBBlobValue) {
                    return DuckDBType_1.BLOB;
                }
                else if (value instanceof values_1.DuckDBDateValue) {
                    return DuckDBType_1.DATE;
                }
                else if (value instanceof values_1.DuckDBDecimalValue) {
                    return (0, DuckDBType_1.DECIMAL)(value.width, value.scale);
                }
                else if (value instanceof values_1.DuckDBIntervalValue) {
                    return DuckDBType_1.INTERVAL;
                }
                else if (value instanceof values_1.DuckDBListValue) {
                    return (0, DuckDBType_1.LIST)(typeForValue(value.items[0]));
                }
                else if (value instanceof values_1.DuckDBMapValue) {
                    return (0, DuckDBType_1.MAP)(typeForValue(value.entries[0].key), typeForValue(value.entries[0].value));
                }
                else if (value instanceof values_1.DuckDBStructValue) {
                    const entryTypes = {};
                    for (const key in value.entries) {
                        entryTypes[key] = typeForValue(value.entries[key]);
                    }
                    return (0, DuckDBType_1.STRUCT)(entryTypes);
                }
                else if (value instanceof values_1.DuckDBTimestampMillisecondsValue) {
                    return DuckDBType_1.TIMESTAMP_MS;
                }
                else if (value instanceof values_1.DuckDBTimestampNanosecondsValue) {
                    return DuckDBType_1.TIMESTAMP_NS;
                }
                else if (value instanceof values_1.DuckDBTimestampSecondsValue) {
                    return DuckDBType_1.TIMESTAMP_S;
                }
                else if (value instanceof values_1.DuckDBTimestampTZValue) {
                    return DuckDBType_1.TIMESTAMPTZ;
                }
                else if (value instanceof values_1.DuckDBTimestampValue) {
                    return DuckDBType_1.TIMESTAMP;
                }
                else if (value instanceof values_1.DuckDBTimeTZValue) {
                    return DuckDBType_1.TIMETZ;
                }
                else if (value instanceof values_1.DuckDBTimeValue) {
                    return DuckDBType_1.TIME;
                }
                else if (value instanceof values_1.DuckDBUnionValue) {
                    return (0, DuckDBType_1.UNION)({ [value.tag]: typeForValue(value.value) });
                }
                else if (value instanceof values_1.DuckDBUUIDValue) {
                    return DuckDBType_1.UUID;
                }
                break;
        }
    }
    return DuckDBType_1.ANY;
}
