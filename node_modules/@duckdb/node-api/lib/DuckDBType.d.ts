import { DuckDBLogicalType } from './DuckDBLogicalType';
import { DuckDBTypeId } from './DuckDBTypeId';
import { Json } from './Json';
import { DuckDBDateValue, DuckDBTimestampMillisecondsValue, DuckDBTimestampNanosecondsValue, DuckDBTimestampSecondsValue, DuckDBTimestampTZValue, DuckDBTimestampValue, DuckDBTimeTZValue, DuckDBTimeValue, DuckDBUUIDValue } from './values';
export declare abstract class BaseDuckDBType<T extends DuckDBTypeId> {
    readonly typeId: T;
    readonly alias?: string;
    protected constructor(typeId: T, alias?: string);
    toString(): string;
    toLogicalType(): DuckDBLogicalType;
    toJson(): Json;
}
export declare class DuckDBBooleanType extends BaseDuckDBType<DuckDBTypeId.BOOLEAN> {
    constructor(alias?: string);
    static readonly instance: DuckDBBooleanType;
    static create(alias?: string): DuckDBBooleanType;
}
export declare const BOOLEAN: DuckDBBooleanType;
export declare class DuckDBTinyIntType extends BaseDuckDBType<DuckDBTypeId.TINYINT> {
    constructor(alias?: string);
    static readonly instance: DuckDBTinyIntType;
    static create(alias?: string): DuckDBTinyIntType;
    static readonly Max: number;
    static readonly Min: number;
    get max(): number;
    get min(): number;
}
export declare const TINYINT: DuckDBTinyIntType;
export declare class DuckDBSmallIntType extends BaseDuckDBType<DuckDBTypeId.SMALLINT> {
    constructor(alias?: string);
    static readonly instance: DuckDBSmallIntType;
    static create(alias?: string): DuckDBSmallIntType;
    static readonly Max: number;
    static readonly Min: number;
    get max(): number;
    get min(): number;
}
export declare const SMALLINT: DuckDBSmallIntType;
export declare class DuckDBIntegerType extends BaseDuckDBType<DuckDBTypeId.INTEGER> {
    constructor(alias?: string);
    static readonly instance: DuckDBIntegerType;
    static create(alias?: string): DuckDBIntegerType;
    static readonly Max: number;
    static readonly Min: number;
    get max(): number;
    get min(): number;
}
export declare const INTEGER: DuckDBIntegerType;
export declare class DuckDBBigIntType extends BaseDuckDBType<DuckDBTypeId.BIGINT> {
    constructor(alias?: string);
    static readonly instance: DuckDBBigIntType;
    static create(alias?: string): DuckDBBigIntType;
    static readonly Max: bigint;
    static readonly Min: bigint;
    get max(): bigint;
    get min(): bigint;
}
export declare const BIGINT: DuckDBBigIntType;
export declare class DuckDBUTinyIntType extends BaseDuckDBType<DuckDBTypeId.UTINYINT> {
    constructor(alias?: string);
    static readonly instance: DuckDBUTinyIntType;
    static create(alias?: string): DuckDBUTinyIntType;
    static readonly Max: number;
    static readonly Min = 0;
    get max(): number;
    get min(): number;
}
export declare const UTINYINT: DuckDBUTinyIntType;
export declare class DuckDBUSmallIntType extends BaseDuckDBType<DuckDBTypeId.USMALLINT> {
    constructor(alias?: string);
    static readonly instance: DuckDBUSmallIntType;
    static create(alias?: string): DuckDBUSmallIntType;
    static readonly Max: number;
    static readonly Min = 0;
    get max(): number;
    get min(): number;
}
export declare const USMALLINT: DuckDBUSmallIntType;
export declare class DuckDBUIntegerType extends BaseDuckDBType<DuckDBTypeId.UINTEGER> {
    constructor(alias?: string);
    static readonly instance: DuckDBUIntegerType;
    static create(alias?: string): DuckDBUIntegerType;
    static readonly Max: number;
    static readonly Min = 0;
    get max(): number;
    get min(): number;
}
export declare const UINTEGER: DuckDBUIntegerType;
export declare class DuckDBUBigIntType extends BaseDuckDBType<DuckDBTypeId.UBIGINT> {
    constructor(alias?: string);
    static readonly instance: DuckDBUBigIntType;
    static create(alias?: string): DuckDBUBigIntType;
    static readonly Max: bigint;
    static readonly Min: bigint;
    get max(): bigint;
    get min(): bigint;
}
export declare const UBIGINT: DuckDBUBigIntType;
export declare class DuckDBFloatType extends BaseDuckDBType<DuckDBTypeId.FLOAT> {
    constructor(alias?: string);
    static readonly instance: DuckDBFloatType;
    static create(alias?: string): DuckDBFloatType;
    static readonly Max: number;
    static readonly Min: number;
    get max(): number;
    get min(): number;
}
export declare const FLOAT: DuckDBFloatType;
export declare class DuckDBDoubleType extends BaseDuckDBType<DuckDBTypeId.DOUBLE> {
    constructor(alias?: string);
    static readonly instance: DuckDBDoubleType;
    static create(alias?: string): DuckDBDoubleType;
    static readonly Max: number;
    static readonly Min: number;
    get max(): number;
    get min(): number;
}
export declare const DOUBLE: DuckDBDoubleType;
export declare class DuckDBTimestampType extends BaseDuckDBType<DuckDBTypeId.TIMESTAMP> {
    constructor(alias?: string);
    static readonly instance: DuckDBTimestampType;
    static create(alias?: string): DuckDBTimestampType;
    get epoch(): DuckDBTimestampValue;
    get max(): DuckDBTimestampValue;
    get min(): DuckDBTimestampValue;
    get posInf(): DuckDBTimestampValue;
    get negInf(): DuckDBTimestampValue;
}
export declare const TIMESTAMP: DuckDBTimestampType;
export type DuckDBTimestampMicrosecondsType = DuckDBTimestampType;
export declare const DuckDBTimestampMicrosecondsType: typeof DuckDBTimestampType;
export declare class DuckDBDateType extends BaseDuckDBType<DuckDBTypeId.DATE> {
    constructor(alias?: string);
    static readonly instance: DuckDBDateType;
    static create(alias?: string): DuckDBDateType;
    get epoch(): DuckDBDateValue;
    get max(): DuckDBDateValue;
    get min(): DuckDBDateValue;
    get posInf(): DuckDBDateValue;
    get negInf(): DuckDBDateValue;
}
export declare const DATE: DuckDBDateType;
export declare class DuckDBTimeType extends BaseDuckDBType<DuckDBTypeId.TIME> {
    constructor(alias?: string);
    static readonly instance: DuckDBTimeType;
    static create(alias?: string): DuckDBTimeType;
    get max(): DuckDBTimeValue;
    get min(): DuckDBTimeValue;
}
export declare const TIME: DuckDBTimeType;
export declare class DuckDBIntervalType extends BaseDuckDBType<DuckDBTypeId.INTERVAL> {
    constructor(alias?: string);
    static readonly instance: DuckDBIntervalType;
    static create(alias?: string): DuckDBIntervalType;
}
export declare const INTERVAL: DuckDBIntervalType;
export declare class DuckDBHugeIntType extends BaseDuckDBType<DuckDBTypeId.HUGEINT> {
    constructor(alias?: string);
    static readonly instance: DuckDBHugeIntType;
    static create(alias?: string): DuckDBHugeIntType;
    static readonly Max: bigint;
    static readonly Min: bigint;
    get max(): bigint;
    get min(): bigint;
}
export declare const HUGEINT: DuckDBHugeIntType;
export declare class DuckDBUHugeIntType extends BaseDuckDBType<DuckDBTypeId.UHUGEINT> {
    constructor(alias?: string);
    static readonly instance: DuckDBUHugeIntType;
    static create(alias?: string): DuckDBUHugeIntType;
    static readonly Max: bigint;
    static readonly Min: bigint;
    get max(): bigint;
    get min(): bigint;
}
export declare const UHUGEINT: DuckDBUHugeIntType;
export declare class DuckDBVarCharType extends BaseDuckDBType<DuckDBTypeId.VARCHAR> {
    constructor(alias?: string);
    static readonly instance: DuckDBVarCharType;
    static create(alias?: string): DuckDBVarCharType;
}
export declare const VARCHAR: DuckDBVarCharType;
export declare class DuckDBBlobType extends BaseDuckDBType<DuckDBTypeId.BLOB> {
    constructor(alias?: string);
    static readonly instance: DuckDBBlobType;
    static create(alias?: string): DuckDBBlobType;
}
export declare const BLOB: DuckDBBlobType;
export declare class DuckDBDecimalType extends BaseDuckDBType<DuckDBTypeId.DECIMAL> {
    readonly width: number;
    readonly scale: number;
    constructor(width: number, scale: number, alias?: string);
    toString(): string;
    toLogicalType(): DuckDBLogicalType;
    toJson(): Json;
    static readonly default: DuckDBDecimalType;
}
export declare function DECIMAL(width?: number, scale?: number, alias?: string): DuckDBDecimalType;
export declare class DuckDBTimestampSecondsType extends BaseDuckDBType<DuckDBTypeId.TIMESTAMP_S> {
    constructor(alias?: string);
    static readonly instance: DuckDBTimestampSecondsType;
    static create(alias?: string): DuckDBTimestampSecondsType;
    get epoch(): DuckDBTimestampSecondsValue;
    get max(): DuckDBTimestampSecondsValue;
    get min(): DuckDBTimestampSecondsValue;
    get posInf(): DuckDBTimestampSecondsValue;
    get negInf(): DuckDBTimestampSecondsValue;
}
export declare const TIMESTAMP_S: DuckDBTimestampSecondsType;
export declare class DuckDBTimestampMillisecondsType extends BaseDuckDBType<DuckDBTypeId.TIMESTAMP_MS> {
    constructor(alias?: string);
    static readonly instance: DuckDBTimestampMillisecondsType;
    static create(alias?: string): DuckDBTimestampMillisecondsType;
    get epoch(): DuckDBTimestampMillisecondsValue;
    get max(): DuckDBTimestampMillisecondsValue;
    get min(): DuckDBTimestampMillisecondsValue;
    get posInf(): DuckDBTimestampMillisecondsValue;
    get negInf(): DuckDBTimestampMillisecondsValue;
}
export declare const TIMESTAMP_MS: DuckDBTimestampMillisecondsType;
export declare class DuckDBTimestampNanosecondsType extends BaseDuckDBType<DuckDBTypeId.TIMESTAMP_NS> {
    constructor(alias?: string);
    static readonly instance: DuckDBTimestampNanosecondsType;
    static create(alias?: string): DuckDBTimestampNanosecondsType;
    get epoch(): DuckDBTimestampNanosecondsValue;
    get max(): DuckDBTimestampNanosecondsValue;
    get min(): DuckDBTimestampNanosecondsValue;
    get posInf(): DuckDBTimestampNanosecondsValue;
    get negInf(): DuckDBTimestampNanosecondsValue;
}
export declare const TIMESTAMP_NS: DuckDBTimestampNanosecondsType;
export declare class DuckDBEnumType extends BaseDuckDBType<DuckDBTypeId.ENUM> {
    readonly values: readonly string[];
    readonly valueIndexes: Readonly<Record<string, number>>;
    readonly internalTypeId: DuckDBTypeId;
    constructor(values: readonly string[], internalTypeId: DuckDBTypeId, alias?: string);
    indexForValue(value: string): number;
    toString(): string;
    toLogicalType(): DuckDBLogicalType;
    toJson(): Json;
}
export declare function ENUM8(values: readonly string[], alias?: string): DuckDBEnumType;
export declare function ENUM16(values: readonly string[], alias?: string): DuckDBEnumType;
export declare function ENUM32(values: readonly string[], alias?: string): DuckDBEnumType;
export declare function ENUM(values: readonly string[], alias?: string): DuckDBEnumType;
export declare class DuckDBListType extends BaseDuckDBType<DuckDBTypeId.LIST> {
    readonly valueType: DuckDBType;
    constructor(valueType: DuckDBType, alias?: string);
    toString(): string;
    toLogicalType(): DuckDBLogicalType;
    toJson(): Json;
}
export declare function LIST(valueType: DuckDBType, alias?: string): DuckDBListType;
export declare class DuckDBStructType extends BaseDuckDBType<DuckDBTypeId.STRUCT> {
    readonly entryNames: readonly string[];
    readonly entryTypes: readonly DuckDBType[];
    readonly entryIndexes: Readonly<Record<string, number>>;
    constructor(entryNames: readonly string[], entryTypes: readonly DuckDBType[], alias?: string);
    get entryCount(): number;
    indexForEntry(entryName: string): number;
    typeForEntry(entryName: string): DuckDBType;
    toString(): string;
    toLogicalType(): DuckDBLogicalType;
    toJson(): Json;
}
export declare function STRUCT(entries: Record<string, DuckDBType>, alias?: string): DuckDBStructType;
export declare class DuckDBMapType extends BaseDuckDBType<DuckDBTypeId.MAP> {
    readonly keyType: DuckDBType;
    readonly valueType: DuckDBType;
    constructor(keyType: DuckDBType, valueType: DuckDBType, alias?: string);
    toString(): string;
    toLogicalType(): DuckDBLogicalType;
    toJson(): Json;
}
export declare function MAP(keyType: DuckDBType, valueType: DuckDBType, alias?: string): DuckDBMapType;
export declare class DuckDBArrayType extends BaseDuckDBType<DuckDBTypeId.ARRAY> {
    readonly valueType: DuckDBType;
    readonly length: number;
    constructor(valueType: DuckDBType, length: number, alias?: string);
    toString(): string;
    toLogicalType(): DuckDBLogicalType;
    toJson(): Json;
}
export declare function ARRAY(valueType: DuckDBType, length: number, alias?: string): DuckDBArrayType;
export declare class DuckDBUUIDType extends BaseDuckDBType<DuckDBTypeId.UUID> {
    constructor(alias?: string);
    static readonly instance: DuckDBUUIDType;
    static create(alias?: string): DuckDBUUIDType;
    get max(): DuckDBUUIDValue;
    get min(): DuckDBUUIDValue;
}
export declare const UUID: DuckDBUUIDType;
export declare class DuckDBUnionType extends BaseDuckDBType<DuckDBTypeId.UNION> {
    readonly memberTags: readonly string[];
    readonly tagMemberIndexes: Readonly<Record<string, number>>;
    readonly memberTypes: readonly DuckDBType[];
    constructor(memberTags: readonly string[], memberTypes: readonly DuckDBType[], alias?: string);
    memberIndexForTag(tag: string): number;
    memberTypeForTag(tag: string): DuckDBType;
    get memberCount(): number;
    toString(): string;
    toLogicalType(): DuckDBLogicalType;
    toJson(): Json;
}
export declare function UNION(members: Record<string, DuckDBType>, alias?: string): DuckDBUnionType;
export declare class DuckDBBitType extends BaseDuckDBType<DuckDBTypeId.BIT> {
    constructor(alias?: string);
    static readonly instance: DuckDBBitType;
    static create(alias?: string): DuckDBBitType;
}
export declare const BIT: DuckDBBitType;
export declare class DuckDBTimeTZType extends BaseDuckDBType<DuckDBTypeId.TIME_TZ> {
    constructor(alias?: string);
    toString(): string;
    static readonly instance: DuckDBTimeTZType;
    static create(alias?: string): DuckDBTimeTZType;
    get max(): DuckDBTimeTZValue;
    get min(): DuckDBTimeTZValue;
}
export declare const TIMETZ: DuckDBTimeTZType;
export declare class DuckDBTimestampTZType extends BaseDuckDBType<DuckDBTypeId.TIMESTAMP_TZ> {
    constructor(alias?: string);
    toString(): string;
    static readonly instance: DuckDBTimestampTZType;
    static create(alias?: string): DuckDBTimestampTZType;
    get epoch(): DuckDBTimestampTZValue;
    get max(): DuckDBTimestampTZValue;
    get min(): DuckDBTimestampTZValue;
    get posInf(): DuckDBTimestampTZValue;
    get negInf(): DuckDBTimestampTZValue;
}
export declare const TIMESTAMPTZ: DuckDBTimestampTZType;
export declare class DuckDBAnyType extends BaseDuckDBType<DuckDBTypeId.ANY> {
    constructor(alias?: string);
    static readonly instance: DuckDBAnyType;
    static create(alias?: string): DuckDBAnyType;
}
export declare const ANY: DuckDBAnyType;
export declare class DuckDBBigNumType extends BaseDuckDBType<DuckDBTypeId.BIGNUM> {
    constructor(alias?: string);
    static readonly instance: DuckDBBigNumType;
    static create(alias?: string): DuckDBBigNumType;
    static readonly Max: bigint;
    static readonly Min: bigint;
    get max(): bigint;
    get min(): bigint;
}
export declare const BIGNUM: DuckDBBigNumType;
export declare class DuckDBSQLNullType extends BaseDuckDBType<DuckDBTypeId.SQLNULL> {
    constructor(alias?: string);
    static readonly instance: DuckDBSQLNullType;
    static create(alias?: string): DuckDBSQLNullType;
}
export declare const SQLNULL: DuckDBSQLNullType;
export type DuckDBType = DuckDBBooleanType | DuckDBTinyIntType | DuckDBSmallIntType | DuckDBIntegerType | DuckDBBigIntType | DuckDBUTinyIntType | DuckDBUSmallIntType | DuckDBUIntegerType | DuckDBUBigIntType | DuckDBFloatType | DuckDBDoubleType | DuckDBTimestampType | DuckDBDateType | DuckDBTimeType | DuckDBIntervalType | DuckDBHugeIntType | DuckDBUHugeIntType | DuckDBVarCharType | DuckDBBlobType | DuckDBDecimalType | DuckDBTimestampSecondsType | DuckDBTimestampMillisecondsType | DuckDBTimestampNanosecondsType | DuckDBEnumType | DuckDBListType | DuckDBStructType | DuckDBMapType | DuckDBArrayType | DuckDBUUIDType | DuckDBUnionType | DuckDBBitType | DuckDBTimeTZType | DuckDBTimestampTZType | DuckDBAnyType | DuckDBBigNumType | DuckDBSQLNullType;
