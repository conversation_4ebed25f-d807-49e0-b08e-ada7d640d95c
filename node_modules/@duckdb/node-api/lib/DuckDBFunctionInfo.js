"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBFunctionInfo = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
class DuckDBFunctionInfo {
    function_info;
    constructor(function_info) {
        this.function_info = function_info;
    }
    getExtraInfo() {
        return node_bindings_1.default.scalar_function_get_extra_info(this.function_info);
    }
    setError(error) {
        node_bindings_1.default.scalar_function_set_error(this.function_info, error);
    }
}
exports.DuckDBFunctionInfo = DuckDBFunctionInfo;
