"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBInstance = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const createConfig_1 = require("./createConfig");
const DuckDBConnection_1 = require("./DuckDBConnection");
const DuckDBInstanceCache_1 = require("./DuckDBInstanceCache");
class DuckDBInstance {
    db;
    constructor(db) {
        this.db = db;
    }
    static async create(path, options) {
        const config = (0, createConfig_1.createConfig)(options);
        return new DuckDBInstance(await node_bindings_1.default.open(path, config));
    }
    static async fromCache(path, options) {
        return DuckDBInstanceCache_1.DuckDBInstanceCache.singleton.getOrCreateInstance(path, options);
    }
    async connect() {
        return new DuckDBConnection_1.DuckDBConnection(await node_bindings_1.default.connect(this.db));
    }
    closeSync() {
        node_bindings_1.default.close_sync(this.db);
    }
}
exports.DuckDBInstance = DuckDBInstance;
