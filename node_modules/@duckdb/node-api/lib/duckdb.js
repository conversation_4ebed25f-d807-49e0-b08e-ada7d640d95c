"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uhugeint_to_double = exports.hugeint_to_double = exports.double_to_uhugeint = exports.double_to_hugeint = void 0;
var node_bindings_1 = require("@duckdb/node-bindings");
Object.defineProperty(exports, "double_to_hugeint", { enumerable: true, get: function () { return node_bindings_1.double_to_hugeint; } });
Object.defineProperty(exports, "double_to_uhugeint", { enumerable: true, get: function () { return node_bindings_1.double_to_uhugeint; } });
Object.defineProperty(exports, "hugeint_to_double", { enumerable: true, get: function () { return node_bindings_1.hugeint_to_double; } });
Object.defineProperty(exports, "uhugeint_to_double", { enumerable: true, get: function () { return node_bindings_1.uhugeint_to_double; } });
__exportStar(require("./configurationOptionDescriptions"), exports);
__exportStar(require("./createDuckDBValueConverter"), exports);
__exportStar(require("./DuckDBAppender"), exports);
__exportStar(require("./DuckDBConnection"), exports);
__exportStar(require("./DuckDBDataChunk"), exports);
__exportStar(require("./DuckDBExtractedStatements"), exports);
__exportStar(require("./DuckDBFunctionInfo"), exports);
__exportStar(require("./DuckDBInstance"), exports);
__exportStar(require("./DuckDBInstanceCache"), exports);
__exportStar(require("./DuckDBLogicalType"), exports);
__exportStar(require("./DuckDBMaterializedResult"), exports);
__exportStar(require("./DuckDBPendingResult"), exports);
__exportStar(require("./DuckDBPreparedStatement"), exports);
__exportStar(require("./DuckDBPreparedStatementCollection"), exports);
__exportStar(require("./DuckDBResult"), exports);
__exportStar(require("./DuckDBResultReader"), exports);
__exportStar(require("./DuckDBScalarFunction"), exports);
__exportStar(require("./DuckDBType"), exports);
__exportStar(require("./DuckDBTypeId"), exports);
__exportStar(require("./DuckDBValueConverter"), exports);
__exportStar(require("./DuckDBValueConverters"), exports);
__exportStar(require("./DuckDBVector"), exports);
__exportStar(require("./enums"), exports);
__exportStar(require("./JS"), exports);
__exportStar(require("./JSDuckDBValueConverter"), exports);
__exportStar(require("./Json"), exports);
__exportStar(require("./JsonDuckDBValueConverter"), exports);
__exportStar(require("./sql"), exports);
__exportStar(require("./values"), exports);
__exportStar(require("./version"), exports);
