import duckdb from '@duckdb/node-bindings';
import { DuckDBDataChunk } from './DuckDBDataChunk';
import { DuckDBFunctionInfo } from './DuckDBFunctionInfo';
import { DuckDBType } from './DuckDBType';
import { DuckDBVector } from './DuckDBVector';
export type DuckDBScalarMainFunction = (functionInfo: DuckDBFunctionInfo, inputDataChunk: DuckDBDataChunk, outputVector: DuckDBVector) => void;
export declare class DuckDBScalarFunction {
    readonly scalar_function: duckdb.ScalarFunction;
    constructor();
    static create({ name, mainFunction, returnType, parameterTypes, varArgsType, specialHandling, volatile, extraInfo, }: {
        name: string;
        mainFunction: DuckDBScalarMainFunction;
        returnType: DuckDBType;
        parameterTypes?: readonly DuckDBType[];
        varArgsType?: DuckDBType;
        specialHandling?: boolean;
        volatile?: boolean;
        extraInfo?: object;
    }): DuckDBScalarFunction;
    destroySync(): void;
    setName(name: string): void;
    setMainFunction(mainFunction: DuckDBScalarMainFunction): void;
    setReturnType(returnType: DuckDBType): void;
    addParameter(parameterType: DuckDBType): void;
    setVarArgs(varArgsType: DuckDBType): void;
    setSpecialHandling(): void;
    setVolatile(): void;
    setExtraInfo(extraInfo: object): void;
}
