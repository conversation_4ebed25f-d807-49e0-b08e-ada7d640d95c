import duckdb from '@duckdb/node-bindings';
import { DuckDB<PERSON>ataChunk } from './DuckDBDataChunk';
import { DuckD<PERSON>rrayType, DuckDBEnumType, Duck<PERSON><PERSON>istType, DuckDBMapType, DuckD<PERSON>tructType, DuckDBType, DuckDBUnionType } from './DuckDBType';
import { DuckD<PERSON>rrayValue, DuckDBBitValue, DuckDBDateValue, DuckDBDecimalValue, DuckDBIntervalValue, Duck<PERSON><PERSON>istVal<PERSON>, DuckDBMapValue, DuckDBStructValue, DuckDBTimestampMillisecondsValue, DuckDBTimestampNanosecondsValue, DuckDBTimestampSecondsValue, DuckDBTimestampTZValue, DuckDBTimestampValue, DuckDBTimeTZValue, DuckDBTimeValue, DuckDBUnionValue, DuckDBUUIDValue, DuckDBValue } from './values';
export declare class DuckDBAppender {
    private readonly appender;
    constructor(appender: duckdb.Appender);
    closeSync(): void;
    flushSync(): void;
    get columnCount(): number;
    columnType(columnIndex: number): DuckDBType;
    endRow(): void;
    appendDefault(): void;
    appendBoolean(value: boolean): void;
    appendTinyInt(value: number): void;
    appendSmallInt(value: number): void;
    appendInteger(value: number): void;
    appendBigInt(value: bigint): void;
    appendHugeInt(value: bigint): void;
    appendUTinyInt(value: number): void;
    appendUSmallInt(value: number): void;
    appendUInteger(value: number): void;
    appendUBigInt(value: bigint): void;
    appendUHugeInt(value: bigint): void;
    appendDecimal(value: DuckDBDecimalValue): void;
    appendFloat(value: number): void;
    appendDouble(value: number): void;
    appendDate(value: DuckDBDateValue): void;
    appendTime(value: DuckDBTimeValue): void;
    appendTimeTZ(value: DuckDBTimeTZValue): void;
    appendTimestamp(value: DuckDBTimestampValue): void;
    appendTimestampTZ(value: DuckDBTimestampTZValue): void;
    appendTimestampSeconds(value: DuckDBTimestampSecondsValue): void;
    appendTimestampMilliseconds(value: DuckDBTimestampMillisecondsValue): void;
    appendTimestampNanoseconds(value: DuckDBTimestampNanosecondsValue): void;
    appendInterval(value: DuckDBIntervalValue): void;
    appendVarchar(value: string): void;
    appendBlob(value: Uint8Array): void;
    appendEnum(value: string, type: DuckDBEnumType): void;
    appendList(value: DuckDBListValue | readonly DuckDBValue[], type?: DuckDBListType): void;
    appendStruct(value: DuckDBStructValue | Readonly<Record<string, DuckDBValue>>, type?: DuckDBStructType): void;
    appendMap(value: DuckDBMapValue, type?: DuckDBMapType): void;
    appendArray(value: DuckDBArrayValue | readonly DuckDBValue[], type?: DuckDBArrayType): void;
    appendUnion(value: DuckDBUnionValue, type?: DuckDBUnionType): void;
    appendUUID(value: DuckDBUUIDValue): void;
    appendBit(value: DuckDBBitValue): void;
    appendBigNum(value: bigint): void;
    appendNull(): void;
    appendValue(value: DuckDBValue, type?: DuckDBType): void;
    appendDataChunk(dataChunk: DuckDBDataChunk): void;
}
