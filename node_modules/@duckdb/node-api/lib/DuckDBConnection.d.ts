import duckdb from '@duckdb/node-bindings';
import { DuckDBAppender } from './DuckDBAppender';
import { DuckDBExtractedStatements } from './DuckDBExtractedStatements';
import { DuckDBInstance } from './DuckDBInstance';
import { DuckDBMaterializedResult } from './DuckDBMaterializedResult';
import { DuckDBPendingResult } from './DuckDBPendingResult';
import { DuckDBPreparedStatement } from './DuckDBPreparedStatement';
import { DuckDBResult } from './DuckDBResult';
import { DuckDBResultReader } from './DuckDBResultReader';
import { DuckDBScalarFunction } from './DuckDBScalarFunction';
import { DuckDBType } from './DuckDBType';
import { DuckDBValue } from './values';
export declare class DuckDBConnection {
    private readonly connection;
    private readonly preparedStatements;
    constructor(connection: duckdb.Connection);
    static create(instance?: DuckDBInstance): Promise<DuckDBConnection>;
    /** Same as disconnectSync. */
    closeSync(): void;
    disconnectSync(): void;
    interrupt(): void;
    get progress(): duckdb.QueryProgress;
    run(sql: string, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBMaterializedResult>;
    runAndRead(sql: string, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBResultReader>;
    runAndReadAll(sql: string, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBResultReader>;
    runAndReadUntil(sql: string, targetRowCount: number, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBResultReader>;
    stream(sql: string, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBResult>;
    streamAndRead(sql: string, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBResultReader>;
    streamAndReadAll(sql: string, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBResultReader>;
    streamAndReadUntil(sql: string, targetRowCount: number, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBResultReader>;
    start(sql: string, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBPendingResult>;
    startStream(sql: string, values?: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): Promise<DuckDBPendingResult>;
    prepare(sql: string): Promise<DuckDBPreparedStatement>;
    private createPrepared;
    extractStatements(sql: string): Promise<DuckDBExtractedStatements>;
    private runUntilLast;
    createAppender(table: string, schema?: string | null, catalog?: string | null): Promise<DuckDBAppender>;
    registerScalarFunction(scalarFunction: DuckDBScalarFunction): void;
}
