"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBPendingResult = exports.DuckDBPendingResultState = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const createResult_1 = require("./createResult");
const DuckDBResultReader_1 = require("./DuckDBResultReader");
// Values match similar enum in C API.
var DuckDBPendingResultState;
(function (DuckDBPendingResultState) {
    DuckDBPendingResultState[DuckDBPendingResultState["RESULT_READY"] = 0] = "RESULT_READY";
    DuckDBPendingResultState[DuckDBPendingResultState["RESULT_NOT_READY"] = 1] = "RESULT_NOT_READY";
    DuckDBPendingResultState[DuckDBPendingResultState["NO_TASKS_AVAILABLE"] = 3] = "NO_TASKS_AVAILABLE";
})(DuckDBPendingResultState || (exports.DuckDBPendingResultState = DuckDBPendingResultState = {}));
class DuckDBPendingResult {
    pending_result;
    constructor(pending_result) {
        this.pending_result = pending_result;
    }
    runTask() {
        const pending_state = node_bindings_1.default.pending_execute_task(this.pending_result);
        switch (pending_state) {
            case node_bindings_1.default.PendingState.RESULT_READY:
                return DuckDBPendingResultState.RESULT_READY;
            case node_bindings_1.default.PendingState.RESULT_NOT_READY:
                return DuckDBPendingResultState.RESULT_NOT_READY;
            case node_bindings_1.default.PendingState.ERROR:
                throw new Error(`Failure running pending result task: ${node_bindings_1.default.pending_error(this.pending_result)}`);
            case node_bindings_1.default.PendingState.NO_TASKS_AVAILABLE:
                return DuckDBPendingResultState.NO_TASKS_AVAILABLE;
            default:
                throw new Error(`Unexpected pending state: ${pending_state}`);
        }
    }
    async getResult() {
        return (0, createResult_1.createResult)(await node_bindings_1.default.execute_pending(this.pending_result));
    }
    async read() {
        return new DuckDBResultReader_1.DuckDBResultReader(await this.getResult());
    }
    async readAll() {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.getResult());
        await reader.readAll();
        return reader;
    }
    async readUntil(targetRowCount) {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.getResult());
        await reader.readUntil(targetRowCount);
        return reader;
    }
}
exports.DuckDBPendingResult = DuckDBPendingResult;
