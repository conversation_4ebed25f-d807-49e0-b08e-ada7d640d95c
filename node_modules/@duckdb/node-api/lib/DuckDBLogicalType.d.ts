import duckdb from '@duckdb/node-bindings';
import { DuckD<PERSON>rrayType, DuckDBDecimalType, DuckDBEnumType, DuckDBListType, DuckDBMapType, DuckDBStructType, DuckDBType, DuckDBUnionType } from './DuckDBType';
import { DuckDBTypeId } from './DuckDBTypeId';
export declare class DuckDBLogicalType {
    readonly logical_type: duckdb.LogicalType;
    protected constructor(logical_type: duckdb.LogicalType);
    static create(logical_type: duckdb.LogicalType): DuckDBLogicalType;
    static createDecimal(width: number, scale: number): DuckDBDecimalLogicalType;
    static createEnum(member_names: readonly string[]): DuckDBEnumLogicalType;
    static createList(valueType: DuckDBLogicalType): DuckDBListLogicalType;
    static createStruct(entryNames: readonly string[], entryLogicalTypes: readonly DuckDBLogicalType[]): DuckDBStructLogicalType;
    static createMap(keyType: DuckD<PERSON>ogicalType, valueType: DuckDBLogicalType): DuckDBMapLogicalType;
    static createArray(valueType: DuckDBLogicalType, length: number): DuckDBArrayLogicalType;
    static createUnion(memberTags: readonly string[], memberLogicalTypes: readonly DuckDBLogicalType[]): DuckDBUnionLogicalType;
    get typeId(): DuckDBTypeId;
    get alias(): string | undefined;
    set alias(newAlias: string | null | undefined);
    asType(): DuckDBType;
}
export declare class DuckDBDecimalLogicalType extends DuckDBLogicalType {
    get width(): number;
    get scale(): number;
    get internalTypeId(): DuckDBTypeId;
    asType(): DuckDBDecimalType;
}
export declare class DuckDBEnumLogicalType extends DuckDBLogicalType {
    get valueCount(): number;
    value(index: number): string;
    values(): readonly string[];
    get internalTypeId(): DuckDBTypeId;
    asType(): DuckDBEnumType;
}
export declare class DuckDBListLogicalType extends DuckDBLogicalType {
    get valueType(): DuckDBLogicalType;
    asType(): DuckDBListType;
}
export declare class DuckDBStructLogicalType extends DuckDBLogicalType {
    get entryCount(): number;
    entryName(index: number): string;
    entryLogicalType(index: number): DuckDBLogicalType;
    entryType(index: number): DuckDBType;
    entryNames(): string[];
    entryLogicalTypes(): DuckDBLogicalType[];
    entryTypes(): DuckDBType[];
    asType(): DuckDBStructType;
}
export declare class DuckDBMapLogicalType extends DuckDBLogicalType {
    get keyType(): DuckDBLogicalType;
    get valueType(): DuckDBLogicalType;
    asType(): DuckDBMapType;
}
export declare class DuckDBArrayLogicalType extends DuckDBLogicalType {
    get valueType(): DuckDBLogicalType;
    get length(): number;
    asType(): DuckDBArrayType;
}
export declare class DuckDBUnionLogicalType extends DuckDBLogicalType {
    get memberCount(): number;
    memberTag(index: number): string;
    memberLogicalType(index: number): DuckDBLogicalType;
    memberType(index: number): DuckDBType;
    memberTags(): string[];
    memberLogicalTypes(): DuckDBLogicalType[];
    memberTypes(): DuckDBType[];
    asType(): DuckDBUnionType;
}
