import duckdb from '@duckdb/node-bindings';
import { DuckDBConnection } from './DuckDBConnection';
export declare class DuckDBInstance {
    private readonly db;
    constructor(db: duckdb.Database);
    static create(path?: string, options?: Record<string, string>): Promise<DuckDBInstance>;
    static fromCache(path?: string, options?: Record<string, string>): Promise<DuckDBInstance>;
    connect(): Promise<DuckDBConnection>;
    closeSync(): void;
}
