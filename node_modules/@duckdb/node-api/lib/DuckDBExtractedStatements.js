"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBExtractedStatements = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBPreparedStatement_1 = require("./DuckDBPreparedStatement");
class DuckDBExtractedStatements {
    connection;
    extracted_statements;
    statement_count;
    preparedStatements;
    constructor(connection, extracted_statements, statement_count, preparedStatements) {
        this.connection = connection;
        this.extracted_statements = extracted_statements;
        this.statement_count = statement_count;
        this.preparedStatements = preparedStatements;
    }
    get count() {
        return this.statement_count;
    }
    async prepare(index) {
        const prepared = new DuckDBPreparedStatement_1.DuckDBPreparedStatement(await node_bindings_1.default.prepare_extracted_statement(this.connection, this.extracted_statements, index));
        if (this.preparedStatements) {
            this.preparedStatements.add(prepared);
        }
        return prepared;
    }
}
exports.DuckDBExtractedStatements = DuckDBExtractedStatements;
