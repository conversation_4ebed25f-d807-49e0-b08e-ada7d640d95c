"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBResult = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBDataChunk_1 = require("./DuckDBDataChunk");
const DuckDBLogicalType_1 = require("./DuckDBLogicalType");
const JSDuckDBValueConverter_1 = require("./JSDuckDBValueConverter");
const JsonDuckDBValueConverter_1 = require("./JsonDuckDBValueConverter");
const convertColumnsFromChunks_1 = require("./convertColumnsFromChunks");
const convertColumnsObjectFromChunks_1 = require("./convertColumnsObjectFromChunks");
const convertRowObjectsFromChunks_1 = require("./convertRowObjectsFromChunks");
const convertRowsFromChunks_1 = require("./convertRowsFromChunks");
const getColumnsFromChunks_1 = require("./getColumnsFromChunks");
const getColumnsObjectFromChunks_1 = require("./getColumnsObjectFromChunks");
const getRowObjectsFromChunks_1 = require("./getRowObjectsFromChunks");
const getRowsFromChunks_1 = require("./getRowsFromChunks");
class DuckDBResult {
    result;
    constructor(result) {
        this.result = result;
    }
    get returnType() {
        return node_bindings_1.default.result_return_type(this.result);
    }
    get statementType() {
        return node_bindings_1.default.result_statement_type(this.result);
    }
    get columnCount() {
        return node_bindings_1.default.column_count(this.result);
    }
    columnName(columnIndex) {
        return node_bindings_1.default.column_name(this.result, columnIndex);
    }
    columnNames() {
        const columnNames = [];
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            columnNames.push(this.columnName(columnIndex));
        }
        return columnNames;
    }
    deduplicatedColumnNames() {
        const outputColumnNames = [];
        const columnCount = this.columnCount;
        const columnNameCount = {};
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            const inputColumnName = this.columnName(columnIndex);
            const nameCount = (columnNameCount[inputColumnName] || 0) + 1;
            columnNameCount[inputColumnName] = nameCount;
            if (nameCount > 1) {
                outputColumnNames.push(`${inputColumnName}:${nameCount - 1}`);
            }
            else {
                outputColumnNames.push(inputColumnName);
            }
        }
        return outputColumnNames;
    }
    columnTypeId(columnIndex) {
        return node_bindings_1.default.column_type(this.result, columnIndex);
    }
    columnLogicalType(columnIndex) {
        return DuckDBLogicalType_1.DuckDBLogicalType.create(node_bindings_1.default.column_logical_type(this.result, columnIndex));
    }
    columnType(columnIndex) {
        return DuckDBLogicalType_1.DuckDBLogicalType.create(node_bindings_1.default.column_logical_type(this.result, columnIndex)).asType();
    }
    columnTypeJson(columnIndex) {
        return this.columnType(columnIndex).toJson();
    }
    columnTypes() {
        const columnTypes = [];
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            columnTypes.push(this.columnType(columnIndex));
        }
        return columnTypes;
    }
    columnTypesJson() {
        const columnTypesJson = [];
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            columnTypesJson.push(this.columnTypeJson(columnIndex));
        }
        return columnTypesJson;
    }
    columnNamesAndTypesJson() {
        return {
            columnNames: this.columnNames(),
            columnTypes: this.columnTypesJson(),
        };
    }
    columnNameAndTypeObjectsJson() {
        const columnNameAndTypeObjects = [];
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            columnNameAndTypeObjects.push({
                columnName: this.columnName(columnIndex),
                columnType: this.columnTypeJson(columnIndex),
            });
        }
        return columnNameAndTypeObjects;
    }
    get isStreaming() {
        return node_bindings_1.default.result_is_streaming(this.result);
    }
    get rowsChanged() {
        return node_bindings_1.default.rows_changed(this.result);
    }
    async fetchChunk() {
        const chunk = await node_bindings_1.default.fetch_chunk(this.result);
        return chunk ? new DuckDBDataChunk_1.DuckDBDataChunk(chunk) : null;
    }
    async fetchAllChunks() {
        const chunks = [];
        while (true) {
            const chunk = await this.fetchChunk();
            if (!chunk || chunk.rowCount === 0) {
                return chunks;
            }
            chunks.push(chunk);
        }
    }
    async getColumns() {
        const chunks = await this.fetchAllChunks();
        return (0, getColumnsFromChunks_1.getColumnsFromChunks)(chunks);
    }
    async convertColumns(converter) {
        const chunks = await this.fetchAllChunks();
        return (0, convertColumnsFromChunks_1.convertColumnsFromChunks)(chunks, converter);
    }
    async getColumnsJS() {
        return this.convertColumns(JSDuckDBValueConverter_1.JSDuckDBValueConverter);
    }
    async getColumnsJson() {
        return this.convertColumns(JsonDuckDBValueConverter_1.JsonDuckDBValueConverter);
    }
    async getColumnsObject() {
        const chunks = await this.fetchAllChunks();
        return (0, getColumnsObjectFromChunks_1.getColumnsObjectFromChunks)(chunks, this.deduplicatedColumnNames());
    }
    async convertColumnsObject(converter) {
        const chunks = await this.fetchAllChunks();
        return (0, convertColumnsObjectFromChunks_1.convertColumnsObjectFromChunks)(chunks, this.deduplicatedColumnNames(), converter);
    }
    async getColumnsObjectJS() {
        return this.convertColumnsObject(JSDuckDBValueConverter_1.JSDuckDBValueConverter);
    }
    async getColumnsObjectJson() {
        return this.convertColumnsObject(JsonDuckDBValueConverter_1.JsonDuckDBValueConverter);
    }
    async getRows() {
        const chunks = await this.fetchAllChunks();
        return (0, getRowsFromChunks_1.getRowsFromChunks)(chunks);
    }
    async convertRows(converter) {
        const chunks = await this.fetchAllChunks();
        return (0, convertRowsFromChunks_1.convertRowsFromChunks)(chunks, converter);
    }
    async getRowsJS() {
        return this.convertRows(JSDuckDBValueConverter_1.JSDuckDBValueConverter);
    }
    async getRowsJson() {
        return this.convertRows(JsonDuckDBValueConverter_1.JsonDuckDBValueConverter);
    }
    async getRowObjects() {
        const chunks = await this.fetchAllChunks();
        return (0, getRowObjectsFromChunks_1.getRowObjectsFromChunks)(chunks, this.deduplicatedColumnNames());
    }
    async convertRowObjects(converter) {
        const chunks = await this.fetchAllChunks();
        return (0, convertRowObjectsFromChunks_1.convertRowObjectsFromChunks)(chunks, this.deduplicatedColumnNames(), converter);
    }
    async getRowObjectsJS() {
        return this.convertRowObjects(JSDuckDBValueConverter_1.JSDuckDBValueConverter);
    }
    async getRowObjectsJson() {
        return this.convertRowObjects(JsonDuckDBValueConverter_1.JsonDuckDBValueConverter);
    }
}
exports.DuckDBResult = DuckDBResult;
