"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createValue = createValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBTypeId_1 = require("./DuckDBTypeId");
const values_1 = require("./values");
function createValue(type, input) {
    if (input === null) {
        return node_bindings_1.default.create_null_value();
    }
    const { typeId } = type;
    switch (typeId) {
        case DuckDBTypeId_1.DuckDBTypeId.BOOLEAN:
            if (typeof input === 'boolean') {
                return node_bindings_1.default.create_bool(input);
            }
            throw new Error(`input is not a boolean`);
        case DuckDBTypeId_1.DuckDBTypeId.TINYINT:
            if (typeof input === 'number') {
                return node_bindings_1.default.create_int8(input);
            }
            throw new Error(`input is not a number`);
        case DuckDBTypeId_1.DuckDBTypeId.SMALLINT:
            if (typeof input === 'number') {
                return node_bindings_1.default.create_int16(input);
            }
            throw new Error(`input is not a number`);
        case DuckDBTypeId_1.DuckDBTypeId.INTEGER:
            if (typeof input === 'number') {
                return node_bindings_1.default.create_int32(input);
            }
            throw new Error(`input is not a number`);
        case DuckDBTypeId_1.DuckDBTypeId.BIGINT:
            if (typeof input === 'bigint') {
                return node_bindings_1.default.create_int64(input);
            }
            throw new Error(`input is not a bigint`);
        case DuckDBTypeId_1.DuckDBTypeId.UTINYINT:
            if (typeof input === 'number') {
                return node_bindings_1.default.create_uint8(input);
            }
            throw new Error(`input is not a number`);
        case DuckDBTypeId_1.DuckDBTypeId.USMALLINT:
            if (typeof input === 'number') {
                return node_bindings_1.default.create_uint16(input);
            }
            throw new Error(`input is not a number`);
        case DuckDBTypeId_1.DuckDBTypeId.UINTEGER:
            if (typeof input === 'number') {
                return node_bindings_1.default.create_uint32(input);
            }
            throw new Error(`input is not a number`);
        case DuckDBTypeId_1.DuckDBTypeId.UBIGINT:
            if (typeof input === 'bigint') {
                return node_bindings_1.default.create_uint64(input);
            }
            throw new Error(`input is not a bigint`);
        case DuckDBTypeId_1.DuckDBTypeId.FLOAT:
            if (typeof input === 'number') {
                return node_bindings_1.default.create_float(input);
            }
            throw new Error(`input is not a number`);
        case DuckDBTypeId_1.DuckDBTypeId.DOUBLE:
            if (typeof input === 'number') {
                return node_bindings_1.default.create_double(input);
            }
            throw new Error(`input is not a number`);
        case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP:
            if (input instanceof values_1.DuckDBTimestampValue) {
                return node_bindings_1.default.create_timestamp(input);
            }
            throw new Error(`input is not a DuckDBTimestampValue`);
        case DuckDBTypeId_1.DuckDBTypeId.DATE:
            if (input instanceof values_1.DuckDBDateValue) {
                return node_bindings_1.default.create_date(input);
            }
            throw new Error(`input is not a DuckDBDateValue`);
        case DuckDBTypeId_1.DuckDBTypeId.TIME:
            if (input instanceof values_1.DuckDBTimeValue) {
                return node_bindings_1.default.create_time(input);
            }
            throw new Error(`input is not a DuckDBTimeValue`);
        case DuckDBTypeId_1.DuckDBTypeId.INTERVAL:
            if (input instanceof values_1.DuckDBIntervalValue) {
                return node_bindings_1.default.create_interval(input);
            }
            throw new Error(`input is not a DuckDBIntervalValue`);
        case DuckDBTypeId_1.DuckDBTypeId.HUGEINT:
            if (typeof input === 'bigint') {
                return node_bindings_1.default.create_hugeint(input);
            }
            throw new Error(`input is not a bigint`);
        case DuckDBTypeId_1.DuckDBTypeId.UHUGEINT:
            if (typeof input === 'bigint') {
                return node_bindings_1.default.create_uhugeint(input);
            }
            throw new Error(`input is not a bigint`);
        case DuckDBTypeId_1.DuckDBTypeId.VARCHAR:
            if (typeof input === 'string') {
                return node_bindings_1.default.create_varchar(input);
            }
            throw new Error(`input is not a string`);
        case DuckDBTypeId_1.DuckDBTypeId.BLOB:
            if (input instanceof values_1.DuckDBBlobValue) {
                return node_bindings_1.default.create_blob(input.bytes);
            }
            throw new Error(`input is not a DuckDBBlobValue`);
        case DuckDBTypeId_1.DuckDBTypeId.DECIMAL:
            if (input instanceof values_1.DuckDBDecimalValue) {
                return node_bindings_1.default.create_decimal(input);
            }
            throw new Error(`input is not a DuckDBDecimalValue`);
        case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_S:
            if (input instanceof values_1.DuckDBTimestampSecondsValue) {
                return node_bindings_1.default.create_timestamp_s(input);
            }
            throw new Error(`input is not a DuckDBTimestampSecondsValue`);
        case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_MS:
            if (input instanceof values_1.DuckDBTimestampMillisecondsValue) {
                return node_bindings_1.default.create_timestamp_ms(input);
            }
            throw new Error(`input is not a DuckDBTimestampMillisecondsValue`);
        case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_NS:
            if (input instanceof values_1.DuckDBTimestampNanosecondsValue) {
                return node_bindings_1.default.create_timestamp_ns(input);
            }
            throw new Error(`input is not a DuckDBTimestampNanosecondsValue`);
        case DuckDBTypeId_1.DuckDBTypeId.ENUM:
            if (typeof input === 'string') {
                return node_bindings_1.default.create_enum_value(type.toLogicalType().logical_type, type.indexForValue(input));
            }
            throw new Error(`input is not a string`);
        case DuckDBTypeId_1.DuckDBTypeId.LIST:
            if (input instanceof values_1.DuckDBListValue) {
                if (type.valueType.typeId === DuckDBTypeId_1.DuckDBTypeId.ANY) {
                    throw new Error('Cannot create lists with item type of ANY. Specify a specific type.');
                }
                return node_bindings_1.default.create_list_value(type.valueType.toLogicalType().logical_type, input.items.map((item) => createValue(type.valueType, item)));
            }
            throw new Error(`input is not a DuckDBListValue`);
        case DuckDBTypeId_1.DuckDBTypeId.STRUCT:
            if (input instanceof values_1.DuckDBStructValue) {
                if (type.entryTypes.find((entryType) => entryType.typeId === DuckDBTypeId_1.DuckDBTypeId.ANY)) {
                    throw new Error('Cannot create structs with an entry type of ANY. Specify a specific type.');
                }
                return node_bindings_1.default.create_struct_value(type.toLogicalType().logical_type, Object.values(input.entries).map((value, i) => createValue(type.entryTypes[i], value)));
            }
            throw new Error(`input is not a DuckDBStructValue`);
        case DuckDBTypeId_1.DuckDBTypeId.MAP:
            if (input instanceof values_1.DuckDBMapValue) {
                if (type.keyType.typeId === DuckDBTypeId_1.DuckDBTypeId.ANY) {
                    throw new Error('Cannot create maps with key type of ANY. Specify a specific type.');
                }
                if (type.valueType.typeId === DuckDBTypeId_1.DuckDBTypeId.ANY) {
                    throw new Error('Cannot create maps with value type of ANY. Specify a specific type.');
                }
                return node_bindings_1.default.create_map_value(type.toLogicalType().logical_type, input.entries.map((entry) => createValue(type.keyType, entry.key)), input.entries.map((entry) => createValue(type.valueType, entry.value)));
            }
            throw new Error(`input is not a DuckDBMapValue`);
        case DuckDBTypeId_1.DuckDBTypeId.ARRAY:
            if (input instanceof values_1.DuckDBArrayValue) {
                if (type.valueType.typeId === DuckDBTypeId_1.DuckDBTypeId.ANY) {
                    throw new Error('Cannot create arrays with item type of ANY. Specify a specific type.');
                }
                return node_bindings_1.default.create_array_value(type.valueType.toLogicalType().logical_type, input.items.map((item) => createValue(type.valueType, item)));
            }
            throw new Error(`input is not a DuckDBArrayValue`);
        case DuckDBTypeId_1.DuckDBTypeId.UUID:
            if (input instanceof values_1.DuckDBUUIDValue) {
                return node_bindings_1.default.create_uuid(input.toUint128());
            }
            throw new Error(`input is not a bigint`);
        case DuckDBTypeId_1.DuckDBTypeId.UNION:
            if (input instanceof values_1.DuckDBUnionValue) {
                const tagIndex = type.memberIndexForTag(input.tag);
                const memberType = type.memberTypes[tagIndex];
                if (memberType.typeId === DuckDBTypeId_1.DuckDBTypeId.ANY) {
                    throw new Error('Cannot create union values with type of ANY.');
                }
                return node_bindings_1.default.create_union_value(type.toLogicalType().logical_type, tagIndex, createValue(memberType, input.value));
            }
            throw new Error(`input is not a DuckDBUnionValue`);
        case DuckDBTypeId_1.DuckDBTypeId.BIT:
            if (input instanceof values_1.DuckDBBitValue) {
                return node_bindings_1.default.create_bit(input.data);
            }
            throw new Error(`input is not a DuckDBBitValue`);
        case DuckDBTypeId_1.DuckDBTypeId.TIME_TZ:
            if (input instanceof values_1.DuckDBTimeTZValue) {
                return node_bindings_1.default.create_time_tz_value(input);
            }
            throw new Error(`input is not a DuckDBTimeTZValue`);
        case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_TZ:
            if (input instanceof values_1.DuckDBTimestampTZValue) {
                return node_bindings_1.default.create_timestamp_tz(input);
            }
            throw new Error(`input is not a DuckDBTimestampTZValue`);
        case DuckDBTypeId_1.DuckDBTypeId.ANY:
            throw new Error(`Cannot create values of type ANY. Specify a specific type.`);
        case DuckDBTypeId_1.DuckDBTypeId.BIGNUM:
            if (typeof input === 'bigint') {
                return node_bindings_1.default.create_bignum(input);
            }
            throw new Error(`input is not a bigint`);
        case DuckDBTypeId_1.DuckDBTypeId.SQLNULL:
            return node_bindings_1.default.create_null_value();
        default:
            throw new Error(`unrecognized type id ${typeId}`);
    }
}
