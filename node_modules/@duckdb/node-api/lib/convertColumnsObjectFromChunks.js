"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertColumnsObjectFromChunks = convertColumnsObjectFromChunks;
function convertColumnsObjectFromChunks(chunks, columnNames, converter) {
    const convertedColumnsObject = {};
    for (const columnName of columnNames) {
        convertedColumnsObject[columnName] = [];
    }
    if (chunks.length === 0) {
        return convertedColumnsObject;
    }
    const columnCount = chunks[0].columnCount;
    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            chunks[chunkIndex].visitColumnValues(columnIndex, (value, _rowIndex, _columnIndex, type) => convertedColumnsObject[columnNames[columnIndex]].push(converter(value, type, converter)));
        }
    }
    return convertedColumnsObject;
}
