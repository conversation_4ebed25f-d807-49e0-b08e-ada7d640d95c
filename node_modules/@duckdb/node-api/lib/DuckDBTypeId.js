"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBTypeId = void 0;
// copy of duckdb_type, with names shortened
var DuckDBTypeId;
(function (DuckDBTypeId) {
    DuckDBTypeId[DuckDBTypeId["INVALID"] = 0] = "INVALID";
    DuckDBTypeId[DuckDBTypeId["BOOLEAN"] = 1] = "BOOLEAN";
    DuckDBTypeId[DuckDBTypeId["TINYINT"] = 2] = "TINYINT";
    DuckDBTypeId[DuckDBTypeId["SMALLINT"] = 3] = "SMALLINT";
    DuckDBTypeId[DuckDBTypeId["INTEGER"] = 4] = "INTEGER";
    DuckDBTypeId[DuckDBTypeId["BIGINT"] = 5] = "BIGINT";
    DuckDBTypeId[DuckDBTypeId["UTINYINT"] = 6] = "UTINYINT";
    DuckDBTypeId[DuckDBTypeId["USMALLINT"] = 7] = "USMALLINT";
    DuckDBTypeId[DuckDBTypeId["UINTEGER"] = 8] = "UINTEGER";
    DuckDBTypeId[DuckDBTypeId["UBIGINT"] = 9] = "UBIGINT";
    DuckDBTypeId[DuckDBTypeId["FLOAT"] = 10] = "FLOAT";
    DuckDBTypeId[DuckDBTypeId["DOUBLE"] = 11] = "DOUBLE";
    DuckDBTypeId[DuckDBTypeId["TIMESTAMP"] = 12] = "TIMESTAMP";
    DuckDBTypeId[DuckDBTypeId["DATE"] = 13] = "DATE";
    DuckDBTypeId[DuckDBTypeId["TIME"] = 14] = "TIME";
    DuckDBTypeId[DuckDBTypeId["INTERVAL"] = 15] = "INTERVAL";
    DuckDBTypeId[DuckDBTypeId["HUGEINT"] = 16] = "HUGEINT";
    DuckDBTypeId[DuckDBTypeId["UHUGEINT"] = 32] = "UHUGEINT";
    DuckDBTypeId[DuckDBTypeId["VARCHAR"] = 17] = "VARCHAR";
    DuckDBTypeId[DuckDBTypeId["BLOB"] = 18] = "BLOB";
    DuckDBTypeId[DuckDBTypeId["DECIMAL"] = 19] = "DECIMAL";
    DuckDBTypeId[DuckDBTypeId["TIMESTAMP_S"] = 20] = "TIMESTAMP_S";
    DuckDBTypeId[DuckDBTypeId["TIMESTAMP_MS"] = 21] = "TIMESTAMP_MS";
    DuckDBTypeId[DuckDBTypeId["TIMESTAMP_NS"] = 22] = "TIMESTAMP_NS";
    DuckDBTypeId[DuckDBTypeId["ENUM"] = 23] = "ENUM";
    DuckDBTypeId[DuckDBTypeId["LIST"] = 24] = "LIST";
    DuckDBTypeId[DuckDBTypeId["STRUCT"] = 25] = "STRUCT";
    DuckDBTypeId[DuckDBTypeId["MAP"] = 26] = "MAP";
    DuckDBTypeId[DuckDBTypeId["ARRAY"] = 33] = "ARRAY";
    DuckDBTypeId[DuckDBTypeId["UUID"] = 27] = "UUID";
    DuckDBTypeId[DuckDBTypeId["UNION"] = 28] = "UNION";
    DuckDBTypeId[DuckDBTypeId["BIT"] = 29] = "BIT";
    DuckDBTypeId[DuckDBTypeId["TIME_TZ"] = 30] = "TIME_TZ";
    DuckDBTypeId[DuckDBTypeId["TIMESTAMP_TZ"] = 31] = "TIMESTAMP_TZ";
    DuckDBTypeId[DuckDBTypeId["ANY"] = 34] = "ANY";
    DuckDBTypeId[DuckDBTypeId["BIGNUM"] = 35] = "BIGNUM";
    DuckDBTypeId[DuckDBTypeId["SQLNULL"] = 36] = "SQLNULL";
})(DuckDBTypeId || (exports.DuckDBTypeId = DuckDBTypeId = {}));
