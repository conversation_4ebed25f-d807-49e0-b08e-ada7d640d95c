import duckdb from '@duckdb/node-bindings';
import { DuckDBDataChunk } from './DuckDBDataChunk';
import { DuckDBLogicalType } from './DuckDBLogicalType';
import { DuckDBType } from './DuckDBType';
import { DuckDBTypeId } from './DuckDBTypeId';
import { DuckDBValueConverter } from './DuckDBValueConverter';
import { JS } from './JS';
import { Json } from './Json';
import { ResultReturnType, StatementType } from './enums';
import { DuckDBValue } from './values';
export declare class DuckDBResult {
    protected readonly result: duckdb.Result;
    constructor(result: duckdb.Result);
    get returnType(): ResultReturnType;
    get statementType(): StatementType;
    get columnCount(): number;
    columnName(columnIndex: number): string;
    columnNames(): string[];
    deduplicatedColumnNames(): string[];
    columnTypeId(columnIndex: number): DuckDBTypeId;
    columnLogicalType(columnIndex: number): DuckDBLogicalType;
    columnType(columnIndex: number): DuckDBType;
    columnTypeJson(columnIndex: number): Json;
    columnTypes(): DuckDBType[];
    columnTypesJson(): Json;
    columnNamesAndTypesJson(): Json;
    columnNameAndTypeObjectsJson(): Json;
    get isStreaming(): boolean;
    get rowsChanged(): number;
    fetchChunk(): Promise<DuckDBDataChunk | null>;
    fetchAllChunks(): Promise<DuckDBDataChunk[]>;
    getColumns(): Promise<DuckDBValue[][]>;
    convertColumns<T>(converter: DuckDBValueConverter<T>): Promise<(T | null)[][]>;
    getColumnsJS(): Promise<JS[][]>;
    getColumnsJson(): Promise<Json[][]>;
    getColumnsObject(): Promise<Record<string, DuckDBValue[]>>;
    convertColumnsObject<T>(converter: DuckDBValueConverter<T>): Promise<Record<string, (T | null)[]>>;
    getColumnsObjectJS(): Promise<Record<string, JS[]>>;
    getColumnsObjectJson(): Promise<Record<string, Json[]>>;
    getRows(): Promise<DuckDBValue[][]>;
    convertRows<T>(converter: DuckDBValueConverter<T>): Promise<(T | null)[][]>;
    getRowsJS(): Promise<JS[][]>;
    getRowsJson(): Promise<Json[][]>;
    getRowObjects(): Promise<Record<string, DuckDBValue>[]>;
    convertRowObjects<T>(converter: DuckDBValueConverter<T>): Promise<Record<string, T | null>[]>;
    getRowObjectsJS(): Promise<Record<string, JS>[]>;
    getRowObjectsJson(): Promise<Record<string, Json>[]>;
}
