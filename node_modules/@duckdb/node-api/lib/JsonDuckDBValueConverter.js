"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JsonDuckDBValueConverter = void 0;
const createDuckDBValueConverter_1 = require("./createDuckDBValueConverter");
const DuckDBTypeId_1 = require("./DuckDBTypeId");
const DuckDBValueConverters_1 = require("./DuckDBValueConverters");
const JsonConvertersByTypeId = {
    [DuckDBTypeId_1.DuckDBTypeId.INVALID]: DuckDBValueConverters_1.unsupportedConverter,
    [DuckDBTypeId_1.DuckDBTypeId.BOOLEAN]: DuckDBValueConverters_1.booleanFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.TINYINT]: DuckDBValueConverters_1.numberFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.SMALLINT]: DuckDBValueConverters_1.numberFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.INTEGER]: DuckDBValueConverters_1.numberFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.BIGINT]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.UTINYINT]: DuckDBValueConverters_1.numberFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.USMALLINT]: DuckDBValueConverters_1.numberFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.UINTEGER]: DuckDBValueConverters_1.numberFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.UBIGINT]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.FLOAT]: DuckDBValueConverters_1.jsonNumberFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.DOUBLE]: DuckDBValueConverters_1.jsonNumberFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.DATE]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.TIME]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.INTERVAL]: DuckDBValueConverters_1.jsonObjectFromIntervalValue,
    [DuckDBTypeId_1.DuckDBTypeId.HUGEINT]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.UHUGEINT]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.VARCHAR]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.BLOB]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.DECIMAL]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_S]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_MS]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_NS]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.ENUM]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.LIST]: DuckDBValueConverters_1.arrayFromListValue,
    [DuckDBTypeId_1.DuckDBTypeId.STRUCT]: DuckDBValueConverters_1.objectFromStructValue,
    [DuckDBTypeId_1.DuckDBTypeId.MAP]: DuckDBValueConverters_1.objectArrayFromMapValue,
    [DuckDBTypeId_1.DuckDBTypeId.ARRAY]: DuckDBValueConverters_1.arrayFromArrayValue,
    [DuckDBTypeId_1.DuckDBTypeId.UUID]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.UNION]: DuckDBValueConverters_1.objectFromUnionValue,
    [DuckDBTypeId_1.DuckDBTypeId.BIT]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.TIME_TZ]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_TZ]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.ANY]: DuckDBValueConverters_1.unsupportedConverter,
    [DuckDBTypeId_1.DuckDBTypeId.BIGNUM]: DuckDBValueConverters_1.stringFromValue,
    [DuckDBTypeId_1.DuckDBTypeId.SQLNULL]: DuckDBValueConverters_1.nullConverter,
};
exports.JsonDuckDBValueConverter = (0, createDuckDBValueConverter_1.createDuckDBValueConverter)(JsonConvertersByTypeId);
