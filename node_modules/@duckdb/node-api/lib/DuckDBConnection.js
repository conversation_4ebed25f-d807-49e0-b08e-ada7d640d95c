"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBConnection = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBAppender_1 = require("./DuckDBAppender");
const DuckDBExtractedStatements_1 = require("./DuckDBExtractedStatements");
const DuckDBInstance_1 = require("./DuckDBInstance");
const DuckDBMaterializedResult_1 = require("./DuckDBMaterializedResult");
const DuckDBPreparedStatement_1 = require("./DuckDBPreparedStatement");
const DuckDBPreparedStatementWeakRefCollection_1 = require("./DuckDBPreparedStatementWeakRefCollection");
const DuckDBResultReader_1 = require("./DuckDBResultReader");
class DuckDBConnection {
    connection;
    preparedStatements;
    constructor(connection) {
        this.connection = connection;
        this.preparedStatements = new DuckDBPreparedStatementWeakRefCollection_1.DuckDBPreparedStatementWeakRefCollection();
    }
    static async create(instance) {
        if (instance) {
            return instance.connect();
        }
        return (await DuckDBInstance_1.DuckDBInstance.fromCache()).connect();
    }
    /** Same as disconnectSync. */
    closeSync() {
        this.disconnectSync();
    }
    disconnectSync() {
        this.preparedStatements.destroySync();
        node_bindings_1.default.disconnect_sync(this.connection);
    }
    interrupt() {
        node_bindings_1.default.interrupt(this.connection);
    }
    get progress() {
        return node_bindings_1.default.query_progress(this.connection);
    }
    async run(sql, values, types) {
        if (values) {
            const prepared = await this.runUntilLast(sql);
            try {
                prepared.bind(values, types);
                const result = await prepared.run();
                return result;
            }
            finally {
                prepared.destroySync();
            }
        }
        else {
            return new DuckDBMaterializedResult_1.DuckDBMaterializedResult(await node_bindings_1.default.query(this.connection, sql));
        }
    }
    async runAndRead(sql, values, types) {
        return new DuckDBResultReader_1.DuckDBResultReader(await this.run(sql, values, types));
    }
    async runAndReadAll(sql, values, types) {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.run(sql, values, types));
        await reader.readAll();
        return reader;
    }
    async runAndReadUntil(sql, targetRowCount, values, types) {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.run(sql, values, types));
        await reader.readUntil(targetRowCount);
        return reader;
    }
    async stream(sql, values, types) {
        const prepared = await this.runUntilLast(sql);
        try {
            if (values) {
                prepared.bind(values, types);
            }
            const result = await prepared.stream();
            return result;
        }
        finally {
            prepared.destroySync();
        }
    }
    async streamAndRead(sql, values, types) {
        return new DuckDBResultReader_1.DuckDBResultReader(await this.stream(sql, values, types));
    }
    async streamAndReadAll(sql, values, types) {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.stream(sql, values, types));
        await reader.readAll();
        return reader;
    }
    async streamAndReadUntil(sql, targetRowCount, values, types) {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.stream(sql, values, types));
        await reader.readUntil(targetRowCount);
        return reader;
    }
    async start(sql, values, types) {
        const prepared = await this.runUntilLast(sql);
        try {
            if (values) {
                prepared.bind(values, types);
            }
            return prepared.start();
        }
        finally {
            prepared.destroySync();
        }
    }
    async startStream(sql, values, types) {
        const prepared = await this.runUntilLast(sql);
        try {
            if (values) {
                prepared.bind(values, types);
            }
            return prepared.startStream();
        }
        finally {
            prepared.destroySync();
        }
    }
    async prepare(sql) {
        const prepared = await this.createPrepared(sql);
        this.preparedStatements.add(prepared);
        return prepared;
    }
    async createPrepared(sql) {
        return new DuckDBPreparedStatement_1.DuckDBPreparedStatement(await node_bindings_1.default.prepare(this.connection, sql));
    }
    async extractStatements(sql) {
        const { extracted_statements, statement_count } = await node_bindings_1.default.extract_statements(this.connection, sql);
        if (statement_count === 0) {
            throw new Error(`Failed to extract statements: ${node_bindings_1.default.extract_statements_error(extracted_statements)}`);
        }
        return new DuckDBExtractedStatements_1.DuckDBExtractedStatements(this.connection, extracted_statements, statement_count, this.preparedStatements);
    }
    async runUntilLast(sql) {
        const extractedStatements = await this.extractStatements(sql);
        const statementCount = extractedStatements.count;
        if (statementCount > 1) {
            for (let i = 0; i < statementCount - 1; i++) {
                const prepared = await extractedStatements.prepare(i);
                try {
                    await prepared.run();
                }
                finally {
                    prepared.destroySync();
                }
            }
        }
        return extractedStatements.prepare(statementCount - 1);
    }
    async createAppender(table, schema, catalog) {
        return new DuckDBAppender_1.DuckDBAppender(node_bindings_1.default.appender_create_ext(this.connection, catalog ?? null, schema ?? null, table));
    }
    registerScalarFunction(scalarFunction) {
        node_bindings_1.default.register_scalar_function(this.connection, scalarFunction.scalar_function);
    }
}
exports.DuckDBConnection = DuckDBConnection;
