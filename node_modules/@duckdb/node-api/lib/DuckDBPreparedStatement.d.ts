import duckdb from '@duckdb/node-bindings';
import { DuckDBMaterializedResult } from './DuckDBMaterializedResult';
import { DuckDBPendingResult } from './DuckDBPendingResult';
import { DuckDBResult } from './DuckDBResult';
import { DuckDBResultReader } from './DuckDBResultReader';
import { DuckDBArrayType, DuckDBEnumType, DuckDBListType, DuckD<PERSON>apType, DuckDBStructType, DuckDBType, DuckDBUnionType } from './DuckDBType';
import { DuckDBTypeId } from './DuckDBTypeId';
import { StatementType } from './enums';
import { DuckDBArrayValue, DuckDBBitValue, DuckDBDateValue, DuckDBDecimalValue, DuckDBIntervalValue, DuckD<PERSON>istValue, DuckDBMapValue, DuckDBStructValue, DuckDBTimestampMillisecondsValue, DuckDBTimestampNanosecondsValue, DuckD<PERSON><PERSON>imestampSecondsValue, Duck<PERSON><PERSON><PERSON><PERSON>tampTZValue, <PERSON><PERSON><PERSON><PERSON><PERSON>tampValue, DuckDBTimeTZValue, DuckDBTimeValue, DuckDBUnionValue, DuckDBUUIDValue, DuckDBValue } from './values';
export declare class DuckDBPreparedStatement {
    private readonly prepared_statement;
    constructor(prepared_statement: duckdb.PreparedStatement);
    destroySync(): void;
    get statementType(): StatementType;
    get parameterCount(): number;
    parameterName(parameterIndex: number): string;
    parameterTypeId(parameterIndex: number): DuckDBTypeId;
    parameterType(parameterIndex: number): DuckDBType;
    clearBindings(): void;
    parameterIndex(parameterName: string): number;
    bindBoolean(parameterIndex: number, value: boolean): void;
    bindTinyInt(parameterIndex: number, value: number): void;
    bindSmallInt(parameterIndex: number, value: number): void;
    bindInteger(parameterIndex: number, value: number): void;
    bindBigInt(parameterIndex: number, value: bigint): void;
    bindHugeInt(parameterIndex: number, value: bigint): void;
    bindUTinyInt(parameterIndex: number, value: number): void;
    bindUSmallInt(parameterIndex: number, value: number): void;
    bindUInteger(parameterIndex: number, value: number): void;
    bindUBigInt(parameterIndex: number, value: bigint): void;
    bindUHugeInt(parameterIndex: number, value: bigint): void;
    bindBigNum(parameterIndex: number, value: bigint): void;
    bindDecimal(parameterIndex: number, value: DuckDBDecimalValue): void;
    bindFloat(parameterIndex: number, value: number): void;
    bindDouble(parameterIndex: number, value: number): void;
    bindDate(parameterIndex: number, value: DuckDBDateValue): void;
    bindTime(parameterIndex: number, value: DuckDBTimeValue): void;
    bindTimeTZ(parameterIndex: number, value: DuckDBTimeTZValue): void;
    bindTimestamp(parameterIndex: number, value: DuckDBTimestampValue): void;
    bindTimestampTZ(parameterIndex: number, value: DuckDBTimestampTZValue): void;
    bindTimestampSeconds(parameterIndex: number, value: DuckDBTimestampSecondsValue): void;
    bindTimestampMilliseconds(parameterIndex: number, value: DuckDBTimestampMillisecondsValue): void;
    bindTimestampNanoseconds(parameterIndex: number, value: DuckDBTimestampNanosecondsValue): void;
    bindInterval(parameterIndex: number, value: DuckDBIntervalValue): void;
    bindVarchar(parameterIndex: number, value: string): void;
    bindBlob(parameterIndex: number, value: Uint8Array): void;
    bindEnum(parameterIndex: number, value: string, type: DuckDBEnumType): void;
    bindArray(parameterIndex: number, value: DuckDBArrayValue | readonly DuckDBValue[], type?: DuckDBArrayType): void;
    bindList(parameterIndex: number, value: DuckDBListValue | readonly DuckDBValue[], type?: DuckDBListType): void;
    bindStruct(parameterIndex: number, value: DuckDBStructValue | Readonly<Record<string, DuckDBValue>>, type?: DuckDBStructType): void;
    bindMap(parameterIndex: number, value: DuckDBMapValue, type?: DuckDBMapType): void;
    bindUnion(parameterIndex: number, value: DuckDBUnionValue, type?: DuckDBUnionType): void;
    bindUUID(parameterIndex: number, value: DuckDBUUIDValue): void;
    bindBit(parameterIndex: number, value: DuckDBBitValue): void;
    bindNull(parameterIndex: number): void;
    bindValue(parameterIndex: number, value: DuckDBValue, type?: DuckDBType): void;
    bind(values: DuckDBValue[] | Record<string, DuckDBValue>, types?: DuckDBType[] | Record<string, DuckDBType | undefined>): void;
    run(): Promise<DuckDBMaterializedResult>;
    runAndRead(): Promise<DuckDBResultReader>;
    runAndReadAll(): Promise<DuckDBResultReader>;
    runAndReadUntil(targetRowCount: number): Promise<DuckDBResultReader>;
    stream(): Promise<DuckDBResult>;
    streamAndRead(): Promise<DuckDBResultReader>;
    streamAndReadAll(): Promise<DuckDBResultReader>;
    streamAndReadUntil(targetRowCount: number): Promise<DuckDBResultReader>;
    start(): DuckDBPendingResult;
    startStream(): DuckDBPendingResult;
}
