"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertRowObjectsFromChunks = convertRowObjectsFromChunks;
function convertRowObjectsFromChunks(chunks, columnNames, converter) {
    const rowObjects = [];
    for (const chunk of chunks) {
        const rowCount = chunk.rowCount;
        for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
            const rowObject = {};
            chunk.visitRowValues(rowIndex, (value, _rowIndex, columnIndex, type) => {
                rowObject[columnNames[columnIndex]] = converter(value, type, converter);
            });
            rowObjects.push(rowObject);
        }
    }
    return rowObjects;
}
