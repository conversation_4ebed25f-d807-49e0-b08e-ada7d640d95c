import duckdb from '@duckdb/node-bindings';
import { DuckDBPreparedStatement } from './DuckDBPreparedStatement';
import { DuckDBPreparedStatementCollection } from './DuckDBPreparedStatementCollection';
export declare class DuckDBExtractedStatements {
    private readonly connection;
    private readonly extracted_statements;
    private readonly statement_count;
    private readonly preparedStatements?;
    constructor(connection: duckdb.Connection, extracted_statements: duckdb.ExtractedStatements, statement_count: number, preparedStatements?: DuckDBPreparedStatementCollection);
    get count(): number;
    prepare(index: number): Promise<DuckDBPreparedStatement>;
}
