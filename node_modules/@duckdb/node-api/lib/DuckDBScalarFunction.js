"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBScalarFunction = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBDataChunk_1 = require("./DuckDBDataChunk");
const DuckDBFunctionInfo_1 = require("./DuckDBFunctionInfo");
const DuckDBVector_1 = require("./DuckDBVector");
class DuckDBScalarFunction {
    scalar_function;
    constructor() {
        this.scalar_function = node_bindings_1.default.create_scalar_function();
    }
    static create({ name, mainFunction, returnType, parameterTypes, varArgsType, specialHandling, volatile, extraInfo, }) {
        const scalarFunction = new DuckDBScalarFunction();
        scalarFunction.setName(name);
        scalarFunction.setMainFunction(mainFunction);
        scalarFunction.setReturnType(returnType);
        if (parameterTypes) {
            for (const parameterType of parameterTypes) {
                scalarFunction.addParameter(parameterType);
            }
        }
        if (varArgsType) {
            scalarFunction.setVarArgs(varArgsType);
        }
        if (specialHandling) {
            scalarFunction.setSpecialHandling();
        }
        if (volatile) {
            scalarFunction.setVolatile();
        }
        if (extraInfo) {
            scalarFunction.setExtraInfo(extraInfo);
        }
        return scalarFunction;
    }
    destroySync() {
        node_bindings_1.default.destroy_scalar_function_sync(this.scalar_function);
    }
    setName(name) {
        node_bindings_1.default.scalar_function_set_name(this.scalar_function, name);
    }
    setMainFunction(mainFunction) {
        node_bindings_1.default.scalar_function_set_function(this.scalar_function, (info, input, output) => {
            const functionInfo = new DuckDBFunctionInfo_1.DuckDBFunctionInfo(info);
            const inputDataChunk = new DuckDBDataChunk_1.DuckDBDataChunk(input);
            const outputVector = DuckDBVector_1.DuckDBVector.create(output, inputDataChunk.rowCount);
            mainFunction(functionInfo, inputDataChunk, outputVector);
        });
    }
    setReturnType(returnType) {
        node_bindings_1.default.scalar_function_set_return_type(this.scalar_function, returnType.toLogicalType().logical_type);
    }
    addParameter(parameterType) {
        node_bindings_1.default.scalar_function_add_parameter(this.scalar_function, parameterType.toLogicalType().logical_type);
    }
    setVarArgs(varArgsType) {
        node_bindings_1.default.scalar_function_set_varargs(this.scalar_function, varArgsType.toLogicalType().logical_type);
    }
    setSpecialHandling() {
        node_bindings_1.default.scalar_function_set_special_handling(this.scalar_function);
    }
    setVolatile() {
        node_bindings_1.default.scalar_function_set_volatile(this.scalar_function);
    }
    setExtraInfo(extraInfo) {
        node_bindings_1.default.scalar_function_set_extra_info(this.scalar_function, extraInfo);
    }
}
exports.DuckDBScalarFunction = DuckDBScalarFunction;
