import duckdb from '@duckdb/node-bindings';
import { DuckDBType } from './DuckDBType';
import { DuckDBValueConverter } from './DuckDBValueConverter';
import { DuckDBVector } from './DuckDBVector';
import { DuckDBValue } from './values';
export declare class DuckDBDataChunk {
    readonly chunk: duckdb.DataChunk;
    private readonly vectors;
    constructor(chunk: duckdb.DataChunk);
    static create(types: readonly DuckDBType[], rowCount?: number): DuckDBDataChunk;
    reset(): void;
    get columnCount(): number;
    get rowCount(): number;
    set rowCount(count: number);
    getColumnVector(columnIndex: number): DuckDBVector;
    visitColumnValues(columnIndex: number, visitValue: (value: DuckDBValue, rowIndex: number, columnIndex: number, type: DuckDBType) => void): void;
    appendColumnValues(columnIndex: number, values: DuckDBValue[]): void;
    getColumnValues(columnIndex: number): DuckDBValue[];
    convertColumnValues<T>(columnIndex: number, converter: DuckDBValueConverter<T>): (T | null)[];
    setColumnValues(columnIndex: number, values: readonly DuckDBValue[]): void;
    visitColumns(visitColumn: (column: DuckDBValue[], columnIndex: number, type: DuckDBType) => void): void;
    appendToColumns(columns: (DuckDBValue[] | undefined)[]): void;
    getColumns(): DuckDBValue[][];
    convertColumns<T>(converter: DuckDBValueConverter<T>): (T | null)[][];
    setColumns(columns: readonly (readonly DuckDBValue[])[]): void;
    appendToColumnsObject(columnNames: readonly string[], columnsObject: Record<string, DuckDBValue[] | undefined>): void;
    getColumnsObject(columnNames: readonly string[]): Record<string, DuckDBValue[]>;
    visitColumnMajor(visitValue: (value: DuckDBValue, rowIndex: number, columnIndex: number, type: DuckDBType) => void): void;
    visitRowValues(rowIndex: number, visitValue: (value: DuckDBValue, rowIndex: number, columnIndex: number, type: DuckDBType) => void): void;
    appendRowValues(rowIndex: number, values: DuckDBValue[]): void;
    getRowValues(rowIndex: number): DuckDBValue[];
    convertRowValues<T>(rowIndex: number, converter: DuckDBValueConverter<T>): (T | null)[];
    visitRows(visitRow: (row: DuckDBValue[], rowIndex: number) => void): void;
    appendToRows(rows: DuckDBValue[][]): void;
    getRows(): DuckDBValue[][];
    convertRows<T>(converter: DuckDBValueConverter<T>): (T | null)[][];
    setRows(rows: readonly (readonly DuckDBValue[])[]): void;
    appendToRowObjects(columnNames: readonly string[], rowObjects: Record<string, DuckDBValue>[]): void;
    getRowObjects(columnNames: readonly string[]): Record<string, DuckDBValue>[];
    visitRowMajor(visitValue: (value: DuckDBValue, rowIndex: number, columnIndex: number, type: DuckDBType) => void): void;
}
