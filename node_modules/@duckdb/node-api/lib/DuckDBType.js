"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBStructType = exports.DuckDBListType = exports.DuckDBEnumType = exports.TIMESTAMP_NS = exports.DuckDBTimestampNanosecondsType = exports.TIMESTAMP_MS = exports.DuckDBTimestampMillisecondsType = exports.TIMESTAMP_S = exports.DuckDBTimestampSecondsType = exports.DuckDBDecimalType = exports.BLOB = exports.DuckDBBlobType = exports.VARCHAR = exports.DuckDBVarCharType = exports.UHUGEINT = exports.DuckDBUHugeIntType = exports.HUGEINT = exports.DuckDBHugeIntType = exports.INTERVAL = exports.DuckDBIntervalType = exports.TIME = exports.DuckDBTimeType = exports.DATE = exports.DuckDBDateType = exports.DuckDBTimestampMicrosecondsType = exports.TIMESTAMP = exports.DuckDBTimestampType = exports.DOUBLE = exports.DuckDBDoubleType = exports.FLOAT = exports.DuckDBFloatType = exports.UBIGINT = exports.DuckDBUBigIntType = exports.UINTEGER = exports.DuckDBUIntegerType = exports.USMALLINT = exports.DuckDBUSmallIntType = exports.UTINYINT = exports.DuckDBUTinyIntType = exports.BIGINT = exports.DuckDBBigIntType = exports.INTEGER = exports.DuckDBIntegerType = exports.SMALLINT = exports.DuckDBSmallIntType = exports.TINYINT = exports.DuckDBTinyIntType = exports.BOOLEAN = exports.DuckDBBooleanType = exports.BaseDuckDBType = void 0;
exports.SQLNULL = exports.DuckDBSQLNullType = exports.BIGNUM = exports.DuckDBBigNumType = exports.ANY = exports.DuckDBAnyType = exports.TIMESTAMPTZ = exports.DuckDBTimestampTZType = exports.TIMETZ = exports.DuckDBTimeTZType = exports.BIT = exports.DuckDBBitType = exports.DuckDBUnionType = exports.UUID = exports.DuckDBUUIDType = exports.DuckDBArrayType = exports.DuckDBMapType = void 0;
exports.DECIMAL = DECIMAL;
exports.ENUM8 = ENUM8;
exports.ENUM16 = ENUM16;
exports.ENUM32 = ENUM32;
exports.ENUM = ENUM;
exports.LIST = LIST;
exports.STRUCT = STRUCT;
exports.MAP = MAP;
exports.ARRAY = ARRAY;
exports.UNION = UNION;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBLogicalType_1 = require("./DuckDBLogicalType");
const DuckDBTypeId_1 = require("./DuckDBTypeId");
const sql_1 = require("./sql");
const values_1 = require("./values");
class BaseDuckDBType {
    typeId;
    alias;
    constructor(typeId, alias) {
        this.typeId = typeId;
        this.alias = alias;
    }
    toString() {
        return DuckDBTypeId_1.DuckDBTypeId[this.typeId];
    }
    toLogicalType() {
        const logicalType = DuckDBLogicalType_1.DuckDBLogicalType.create(node_bindings_1.default.create_logical_type(this.typeId));
        if (this.alias) {
            logicalType.alias = this.alias;
        }
        return logicalType;
    }
    toJson() {
        return {
            typeId: this.typeId,
            ...(this.alias ? { alias: this.alias } : {}),
        };
    }
}
exports.BaseDuckDBType = BaseDuckDBType;
class DuckDBBooleanType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.BOOLEAN, alias);
    }
    static instance = new DuckDBBooleanType();
    static create(alias) {
        return alias ? new DuckDBBooleanType(alias) : DuckDBBooleanType.instance;
    }
}
exports.DuckDBBooleanType = DuckDBBooleanType;
exports.BOOLEAN = DuckDBBooleanType.instance;
class DuckDBTinyIntType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.TINYINT, alias);
    }
    static instance = new DuckDBTinyIntType();
    static create(alias) {
        return alias ? new DuckDBTinyIntType(alias) : DuckDBTinyIntType.instance;
    }
    static Max = 2 ** 7 - 1;
    static Min = -(2 ** 7);
    get max() {
        return DuckDBTinyIntType.Max;
    }
    get min() {
        return DuckDBTinyIntType.Min;
    }
}
exports.DuckDBTinyIntType = DuckDBTinyIntType;
exports.TINYINT = DuckDBTinyIntType.instance;
class DuckDBSmallIntType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.SMALLINT, alias);
    }
    static instance = new DuckDBSmallIntType();
    static create(alias) {
        return alias ? new DuckDBSmallIntType(alias) : DuckDBSmallIntType.instance;
    }
    static Max = 2 ** 15 - 1;
    static Min = -(2 ** 15);
    get max() {
        return DuckDBSmallIntType.Max;
    }
    get min() {
        return DuckDBSmallIntType.Min;
    }
}
exports.DuckDBSmallIntType = DuckDBSmallIntType;
exports.SMALLINT = DuckDBSmallIntType.instance;
class DuckDBIntegerType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.INTEGER, alias);
    }
    static instance = new DuckDBIntegerType();
    static create(alias) {
        return alias ? new DuckDBIntegerType(alias) : DuckDBIntegerType.instance;
    }
    static Max = 2 ** 31 - 1;
    static Min = -(2 ** 31);
    get max() {
        return DuckDBIntegerType.Max;
    }
    get min() {
        return DuckDBIntegerType.Min;
    }
}
exports.DuckDBIntegerType = DuckDBIntegerType;
exports.INTEGER = DuckDBIntegerType.instance;
class DuckDBBigIntType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.BIGINT, alias);
    }
    static instance = new DuckDBBigIntType();
    static create(alias) {
        return alias ? new DuckDBBigIntType(alias) : DuckDBBigIntType.instance;
    }
    static Max = 2n ** 63n - 1n;
    static Min = -(2n ** 63n);
    get max() {
        return DuckDBBigIntType.Max;
    }
    get min() {
        return DuckDBBigIntType.Min;
    }
}
exports.DuckDBBigIntType = DuckDBBigIntType;
exports.BIGINT = DuckDBBigIntType.instance;
class DuckDBUTinyIntType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.UTINYINT, alias);
    }
    static instance = new DuckDBUTinyIntType();
    static create(alias) {
        return alias ? new DuckDBUTinyIntType(alias) : DuckDBUTinyIntType.instance;
    }
    static Max = 2 ** 8 - 1;
    static Min = 0;
    get max() {
        return DuckDBUTinyIntType.Max;
    }
    get min() {
        return DuckDBUTinyIntType.Min;
    }
}
exports.DuckDBUTinyIntType = DuckDBUTinyIntType;
exports.UTINYINT = DuckDBUTinyIntType.instance;
class DuckDBUSmallIntType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.USMALLINT, alias);
    }
    static instance = new DuckDBUSmallIntType();
    static create(alias) {
        return alias
            ? new DuckDBUSmallIntType(alias)
            : DuckDBUSmallIntType.instance;
    }
    static Max = 2 ** 16 - 1;
    static Min = 0;
    get max() {
        return DuckDBUSmallIntType.Max;
    }
    get min() {
        return DuckDBUSmallIntType.Min;
    }
}
exports.DuckDBUSmallIntType = DuckDBUSmallIntType;
exports.USMALLINT = DuckDBUSmallIntType.instance;
class DuckDBUIntegerType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.UINTEGER, alias);
    }
    static instance = new DuckDBUIntegerType();
    static create(alias) {
        return alias ? new DuckDBUIntegerType(alias) : DuckDBUIntegerType.instance;
    }
    static Max = 2 ** 32 - 1;
    static Min = 0;
    get max() {
        return DuckDBUIntegerType.Max;
    }
    get min() {
        return DuckDBUIntegerType.Min;
    }
}
exports.DuckDBUIntegerType = DuckDBUIntegerType;
exports.UINTEGER = DuckDBUIntegerType.instance;
class DuckDBUBigIntType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.UBIGINT, alias);
    }
    static instance = new DuckDBUBigIntType();
    static create(alias) {
        return alias ? new DuckDBUBigIntType(alias) : DuckDBUBigIntType.instance;
    }
    static Max = 2n ** 64n - 1n;
    static Min = 0n;
    get max() {
        return DuckDBUBigIntType.Max;
    }
    get min() {
        return DuckDBUBigIntType.Min;
    }
}
exports.DuckDBUBigIntType = DuckDBUBigIntType;
exports.UBIGINT = DuckDBUBigIntType.instance;
class DuckDBFloatType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.FLOAT, alias);
    }
    static instance = new DuckDBFloatType();
    static create(alias) {
        return alias ? new DuckDBFloatType(alias) : DuckDBFloatType.instance;
    }
    static Max = Math.fround(3.4028235e38);
    static Min = Math.fround(-3.4028235e38);
    get max() {
        return DuckDBFloatType.Max;
    }
    get min() {
        return DuckDBFloatType.Min;
    }
}
exports.DuckDBFloatType = DuckDBFloatType;
exports.FLOAT = DuckDBFloatType.instance;
class DuckDBDoubleType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.DOUBLE, alias);
    }
    static instance = new DuckDBDoubleType();
    static create(alias) {
        return alias ? new DuckDBDoubleType(alias) : DuckDBDoubleType.instance;
    }
    static Max = Number.MAX_VALUE;
    static Min = -Number.MAX_VALUE;
    get max() {
        return DuckDBDoubleType.Max;
    }
    get min() {
        return DuckDBDoubleType.Min;
    }
}
exports.DuckDBDoubleType = DuckDBDoubleType;
exports.DOUBLE = DuckDBDoubleType.instance;
class DuckDBTimestampType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP, alias);
    }
    static instance = new DuckDBTimestampType();
    static create(alias) {
        return alias
            ? new DuckDBTimestampType(alias)
            : DuckDBTimestampType.instance;
    }
    get epoch() {
        return values_1.DuckDBTimestampValue.Epoch;
    }
    get max() {
        return values_1.DuckDBTimestampValue.Max;
    }
    get min() {
        return values_1.DuckDBTimestampValue.Min;
    }
    get posInf() {
        return values_1.DuckDBTimestampValue.PosInf;
    }
    get negInf() {
        return values_1.DuckDBTimestampValue.NegInf;
    }
}
exports.DuckDBTimestampType = DuckDBTimestampType;
exports.TIMESTAMP = DuckDBTimestampType.instance;
exports.DuckDBTimestampMicrosecondsType = DuckDBTimestampType;
class DuckDBDateType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.DATE, alias);
    }
    static instance = new DuckDBDateType();
    static create(alias) {
        return alias ? new DuckDBDateType(alias) : DuckDBDateType.instance;
    }
    get epoch() {
        return values_1.DuckDBDateValue.Epoch;
    }
    get max() {
        return values_1.DuckDBDateValue.Max;
    }
    get min() {
        return values_1.DuckDBDateValue.Min;
    }
    get posInf() {
        return values_1.DuckDBDateValue.PosInf;
    }
    get negInf() {
        return values_1.DuckDBDateValue.NegInf;
    }
}
exports.DuckDBDateType = DuckDBDateType;
exports.DATE = DuckDBDateType.instance;
class DuckDBTimeType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.TIME, alias);
    }
    static instance = new DuckDBTimeType();
    static create(alias) {
        return alias ? new DuckDBTimeType(alias) : DuckDBTimeType.instance;
    }
    get max() {
        return values_1.DuckDBTimeValue.Max;
    }
    get min() {
        return values_1.DuckDBTimeValue.Min;
    }
}
exports.DuckDBTimeType = DuckDBTimeType;
exports.TIME = DuckDBTimeType.instance;
class DuckDBIntervalType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.INTERVAL, alias);
    }
    static instance = new DuckDBIntervalType();
    static create(alias) {
        return alias ? new DuckDBIntervalType(alias) : DuckDBIntervalType.instance;
    }
}
exports.DuckDBIntervalType = DuckDBIntervalType;
exports.INTERVAL = DuckDBIntervalType.instance;
class DuckDBHugeIntType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.HUGEINT, alias);
    }
    static instance = new DuckDBHugeIntType();
    static create(alias) {
        return alias ? new DuckDBHugeIntType(alias) : DuckDBHugeIntType.instance;
    }
    static Max = 2n ** 127n - 1n;
    static Min = -(2n ** 127n);
    get max() {
        return DuckDBHugeIntType.Max;
    }
    get min() {
        return DuckDBHugeIntType.Min;
    }
}
exports.DuckDBHugeIntType = DuckDBHugeIntType;
exports.HUGEINT = DuckDBHugeIntType.instance;
class DuckDBUHugeIntType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.UHUGEINT, alias);
    }
    static instance = new DuckDBUHugeIntType();
    static create(alias) {
        return alias ? new DuckDBUHugeIntType(alias) : DuckDBUHugeIntType.instance;
    }
    static Max = 2n ** 128n - 1n;
    static Min = 0n;
    get max() {
        return DuckDBUHugeIntType.Max;
    }
    get min() {
        return DuckDBUHugeIntType.Min;
    }
}
exports.DuckDBUHugeIntType = DuckDBUHugeIntType;
exports.UHUGEINT = DuckDBUHugeIntType.instance;
class DuckDBVarCharType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.VARCHAR, alias);
    }
    static instance = new DuckDBVarCharType();
    static create(alias) {
        return alias ? new DuckDBVarCharType(alias) : DuckDBVarCharType.instance;
    }
}
exports.DuckDBVarCharType = DuckDBVarCharType;
exports.VARCHAR = DuckDBVarCharType.instance;
class DuckDBBlobType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.BLOB, alias);
    }
    static instance = new DuckDBBlobType();
    static create(alias) {
        return alias ? new DuckDBBlobType(alias) : DuckDBBlobType.instance;
    }
}
exports.DuckDBBlobType = DuckDBBlobType;
exports.BLOB = DuckDBBlobType.instance;
class DuckDBDecimalType extends BaseDuckDBType {
    width;
    scale;
    constructor(width, scale, alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.DECIMAL, alias);
        this.width = width;
        this.scale = scale;
    }
    toString() {
        return `DECIMAL(${this.width},${this.scale})`;
    }
    toLogicalType() {
        const logicalType = DuckDBLogicalType_1.DuckDBLogicalType.createDecimal(this.width, this.scale);
        if (this.alias) {
            logicalType.alias = this.alias;
        }
        return logicalType;
    }
    toJson() {
        return {
            typeId: this.typeId,
            width: this.width,
            scale: this.scale,
            ...(this.alias ? { alias: this.alias } : {}),
        };
    }
    static default = new DuckDBDecimalType(18, 3);
}
exports.DuckDBDecimalType = DuckDBDecimalType;
function DECIMAL(width, scale, alias) {
    if (width === undefined) {
        return DuckDBDecimalType.default;
    }
    if (scale === undefined) {
        return new DuckDBDecimalType(width, 0);
    }
    return new DuckDBDecimalType(width, scale, alias);
}
class DuckDBTimestampSecondsType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_S, alias);
    }
    static instance = new DuckDBTimestampSecondsType();
    static create(alias) {
        return alias
            ? new DuckDBTimestampSecondsType(alias)
            : DuckDBTimestampSecondsType.instance;
    }
    get epoch() {
        return values_1.DuckDBTimestampSecondsValue.Epoch;
    }
    get max() {
        return values_1.DuckDBTimestampSecondsValue.Max;
    }
    get min() {
        return values_1.DuckDBTimestampSecondsValue.Min;
    }
    get posInf() {
        return values_1.DuckDBTimestampSecondsValue.PosInf;
    }
    get negInf() {
        return values_1.DuckDBTimestampSecondsValue.NegInf;
    }
}
exports.DuckDBTimestampSecondsType = DuckDBTimestampSecondsType;
exports.TIMESTAMP_S = DuckDBTimestampSecondsType.instance;
class DuckDBTimestampMillisecondsType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_MS, alias);
    }
    static instance = new DuckDBTimestampMillisecondsType();
    static create(alias) {
        return alias
            ? new DuckDBTimestampMillisecondsType(alias)
            : DuckDBTimestampMillisecondsType.instance;
    }
    get epoch() {
        return values_1.DuckDBTimestampMillisecondsValue.Epoch;
    }
    get max() {
        return values_1.DuckDBTimestampMillisecondsValue.Max;
    }
    get min() {
        return values_1.DuckDBTimestampMillisecondsValue.Min;
    }
    get posInf() {
        return values_1.DuckDBTimestampMillisecondsValue.PosInf;
    }
    get negInf() {
        return values_1.DuckDBTimestampMillisecondsValue.NegInf;
    }
}
exports.DuckDBTimestampMillisecondsType = DuckDBTimestampMillisecondsType;
exports.TIMESTAMP_MS = DuckDBTimestampMillisecondsType.instance;
class DuckDBTimestampNanosecondsType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_NS, alias);
    }
    static instance = new DuckDBTimestampNanosecondsType();
    static create(alias) {
        return alias
            ? new DuckDBTimestampNanosecondsType(alias)
            : DuckDBTimestampNanosecondsType.instance;
    }
    get epoch() {
        return values_1.DuckDBTimestampNanosecondsValue.Epoch;
    }
    get max() {
        return values_1.DuckDBTimestampNanosecondsValue.Max;
    }
    get min() {
        return values_1.DuckDBTimestampNanosecondsValue.Min;
    }
    get posInf() {
        return values_1.DuckDBTimestampNanosecondsValue.PosInf;
    }
    get negInf() {
        return values_1.DuckDBTimestampNanosecondsValue.NegInf;
    }
}
exports.DuckDBTimestampNanosecondsType = DuckDBTimestampNanosecondsType;
exports.TIMESTAMP_NS = DuckDBTimestampNanosecondsType.instance;
class DuckDBEnumType extends BaseDuckDBType {
    values;
    valueIndexes;
    internalTypeId;
    constructor(values, internalTypeId, alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.ENUM, alias);
        this.values = values;
        const valueIndexes = {};
        for (let i = 0; i < values.length; i++) {
            valueIndexes[values[i]] = i;
        }
        this.valueIndexes = valueIndexes;
        this.internalTypeId = internalTypeId;
    }
    indexForValue(value) {
        return this.valueIndexes[value];
    }
    toString() {
        return `ENUM(${this.values.map(sql_1.quotedString).join(', ')})`;
    }
    toLogicalType() {
        const logicalType = DuckDBLogicalType_1.DuckDBLogicalType.createEnum(this.values);
        if (this.alias) {
            logicalType.alias = this.alias;
        }
        return logicalType;
    }
    toJson() {
        return {
            typeId: this.typeId,
            values: [...this.values],
            internalTypeId: this.internalTypeId,
            ...(this.alias ? { alias: this.alias } : {}),
        };
    }
}
exports.DuckDBEnumType = DuckDBEnumType;
function ENUM8(values, alias) {
    return new DuckDBEnumType(values, DuckDBTypeId_1.DuckDBTypeId.UTINYINT, alias);
}
function ENUM16(values, alias) {
    return new DuckDBEnumType(values, DuckDBTypeId_1.DuckDBTypeId.USMALLINT, alias);
}
function ENUM32(values, alias) {
    return new DuckDBEnumType(values, DuckDBTypeId_1.DuckDBTypeId.UINTEGER, alias);
}
function ENUM(values, alias) {
    if (values.length < 256) {
        return ENUM8(values, alias);
    }
    else if (values.length < 65536) {
        return ENUM16(values, alias);
    }
    else if (values.length < 4294967296) {
        return ENUM32(values, alias);
    }
    else {
        throw new Error(`ENUM types cannot have more than 4294967295 values; received ${values.length}`);
    }
}
class DuckDBListType extends BaseDuckDBType {
    valueType;
    constructor(valueType, alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.LIST, alias);
        this.valueType = valueType;
    }
    toString() {
        return `${this.valueType}[]`;
    }
    toLogicalType() {
        const logicalType = DuckDBLogicalType_1.DuckDBLogicalType.createList(this.valueType.toLogicalType());
        if (this.alias) {
            logicalType.alias = this.alias;
        }
        return logicalType;
    }
    toJson() {
        return {
            typeId: this.typeId,
            valueType: this.valueType.toJson(),
            ...(this.alias ? { alias: this.alias } : {}),
        };
    }
}
exports.DuckDBListType = DuckDBListType;
function LIST(valueType, alias) {
    return new DuckDBListType(valueType, alias);
}
class DuckDBStructType extends BaseDuckDBType {
    entryNames;
    entryTypes;
    entryIndexes;
    constructor(entryNames, entryTypes, alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.STRUCT, alias);
        if (entryNames.length !== entryTypes.length) {
            throw new Error(`Could not create DuckDBStructType: \
        entryNames length (${entryNames.length}) does not match entryTypes length (${entryTypes.length})`);
        }
        this.entryNames = entryNames;
        this.entryTypes = entryTypes;
        const entryIndexes = {};
        for (let i = 0; i < entryNames.length; i++) {
            entryIndexes[entryNames[i]] = i;
        }
        this.entryIndexes = entryIndexes;
    }
    get entryCount() {
        return this.entryNames.length;
    }
    indexForEntry(entryName) {
        return this.entryIndexes[entryName];
    }
    typeForEntry(entryName) {
        return this.entryTypes[this.entryIndexes[entryName]];
    }
    toString() {
        const parts = [];
        for (let i = 0; i < this.entryNames.length; i++) {
            parts.push(`${(0, sql_1.quotedIdentifier)(this.entryNames[i])} ${this.entryTypes[i]}`);
        }
        return `STRUCT(${parts.join(', ')})`;
    }
    toLogicalType() {
        const logicalType = DuckDBLogicalType_1.DuckDBLogicalType.createStruct(this.entryNames, this.entryTypes.map((t) => t.toLogicalType()));
        if (this.alias) {
            logicalType.alias = this.alias;
        }
        return logicalType;
    }
    toJson() {
        return {
            typeId: this.typeId,
            entryNames: [...this.entryNames],
            entryTypes: this.entryTypes.map(t => t.toJson()),
            ...(this.alias ? { alias: this.alias } : {}),
        };
    }
}
exports.DuckDBStructType = DuckDBStructType;
function STRUCT(entries, alias) {
    const entryNames = Object.keys(entries);
    const entryTypes = Object.values(entries);
    return new DuckDBStructType(entryNames, entryTypes, alias);
}
class DuckDBMapType extends BaseDuckDBType {
    keyType;
    valueType;
    constructor(keyType, valueType, alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.MAP, alias);
        this.keyType = keyType;
        this.valueType = valueType;
    }
    toString() {
        return `MAP(${this.keyType}, ${this.valueType})`;
    }
    toLogicalType() {
        const logicalType = DuckDBLogicalType_1.DuckDBLogicalType.createMap(this.keyType.toLogicalType(), this.valueType.toLogicalType());
        if (this.alias) {
            logicalType.alias = this.alias;
        }
        return logicalType;
    }
    toJson() {
        return {
            typeId: this.typeId,
            keyType: this.keyType.toJson(),
            valueType: this.valueType.toJson(),
            ...(this.alias ? { alias: this.alias } : {}),
        };
    }
}
exports.DuckDBMapType = DuckDBMapType;
function MAP(keyType, valueType, alias) {
    return new DuckDBMapType(keyType, valueType, alias);
}
class DuckDBArrayType extends BaseDuckDBType {
    valueType;
    length;
    constructor(valueType, length, alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.ARRAY, alias);
        this.valueType = valueType;
        this.length = length;
    }
    toString() {
        return `${this.valueType}[${this.length}]`;
    }
    toLogicalType() {
        const logicalType = DuckDBLogicalType_1.DuckDBLogicalType.createArray(this.valueType.toLogicalType(), this.length);
        if (this.alias) {
            logicalType.alias = this.alias;
        }
        return logicalType;
    }
    toJson() {
        return {
            typeId: this.typeId,
            valueType: this.valueType.toJson(),
            length: this.length,
            ...(this.alias ? { alias: this.alias } : {}),
        };
    }
}
exports.DuckDBArrayType = DuckDBArrayType;
function ARRAY(valueType, length, alias) {
    return new DuckDBArrayType(valueType, length, alias);
}
class DuckDBUUIDType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.UUID, alias);
    }
    static instance = new DuckDBUUIDType();
    static create(alias) {
        return alias ? new DuckDBUUIDType(alias) : DuckDBUUIDType.instance;
    }
    get max() {
        return values_1.DuckDBUUIDValue.Max;
    }
    get min() {
        return values_1.DuckDBUUIDValue.Min;
    }
}
exports.DuckDBUUIDType = DuckDBUUIDType;
exports.UUID = DuckDBUUIDType.instance;
class DuckDBUnionType extends BaseDuckDBType {
    memberTags;
    tagMemberIndexes;
    memberTypes;
    constructor(memberTags, memberTypes, alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.UNION, alias);
        if (memberTags.length !== memberTypes.length) {
            throw new Error(`Could not create DuckDBUnionType: \
        tags length (${memberTags.length}) does not match valueTypes length (${memberTypes.length})`);
        }
        this.memberTags = memberTags;
        const tagMemberIndexes = {};
        for (let i = 0; i < memberTags.length; i++) {
            tagMemberIndexes[memberTags[i]] = i;
        }
        this.tagMemberIndexes = tagMemberIndexes;
        this.memberTypes = memberTypes;
    }
    memberIndexForTag(tag) {
        return this.tagMemberIndexes[tag];
    }
    memberTypeForTag(tag) {
        return this.memberTypes[this.tagMemberIndexes[tag]];
    }
    get memberCount() {
        return this.memberTags.length;
    }
    toString() {
        const parts = [];
        for (let i = 0; i < this.memberTags.length; i++) {
            parts.push(`${(0, sql_1.quotedIdentifier)(this.memberTags[i])} ${this.memberTypes[i]}`);
        }
        return `UNION(${parts.join(', ')})`;
    }
    toLogicalType() {
        const logicalType = DuckDBLogicalType_1.DuckDBLogicalType.createUnion(this.memberTags, this.memberTypes.map((t) => t.toLogicalType()));
        if (this.alias) {
            logicalType.alias = this.alias;
        }
        return logicalType;
    }
    toJson() {
        return {
            typeId: this.typeId,
            memberTags: [...this.memberTags],
            memberTypes: this.memberTypes.map(t => t.toJson()),
            ...(this.alias ? { alias: this.alias } : {}),
        };
    }
}
exports.DuckDBUnionType = DuckDBUnionType;
function UNION(members, alias) {
    const memberTags = Object.keys(members);
    const memberTypes = Object.values(members);
    return new DuckDBUnionType(memberTags, memberTypes, alias);
}
class DuckDBBitType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.BIT, alias);
    }
    static instance = new DuckDBBitType();
    static create(alias) {
        return alias ? new DuckDBBitType(alias) : DuckDBBitType.instance;
    }
}
exports.DuckDBBitType = DuckDBBitType;
exports.BIT = DuckDBBitType.instance;
class DuckDBTimeTZType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.TIME_TZ, alias);
    }
    toString() {
        return 'TIME WITH TIME ZONE';
    }
    static instance = new DuckDBTimeTZType();
    static create(alias) {
        return alias ? new DuckDBTimeTZType(alias) : DuckDBTimeTZType.instance;
    }
    get max() {
        return values_1.DuckDBTimeTZValue.Max;
    }
    get min() {
        return values_1.DuckDBTimeTZValue.Min;
    }
}
exports.DuckDBTimeTZType = DuckDBTimeTZType;
exports.TIMETZ = DuckDBTimeTZType.instance;
class DuckDBTimestampTZType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_TZ, alias);
    }
    toString() {
        return 'TIMESTAMP WITH TIME ZONE';
    }
    static instance = new DuckDBTimestampTZType();
    static create(alias) {
        return alias
            ? new DuckDBTimestampTZType(alias)
            : DuckDBTimestampTZType.instance;
    }
    get epoch() {
        return values_1.DuckDBTimestampTZValue.Epoch;
    }
    get max() {
        return values_1.DuckDBTimestampTZValue.Max;
    }
    get min() {
        return values_1.DuckDBTimestampTZValue.Min;
    }
    get posInf() {
        return values_1.DuckDBTimestampTZValue.PosInf;
    }
    get negInf() {
        return values_1.DuckDBTimestampTZValue.NegInf;
    }
}
exports.DuckDBTimestampTZType = DuckDBTimestampTZType;
exports.TIMESTAMPTZ = DuckDBTimestampTZType.instance;
class DuckDBAnyType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.ANY, alias);
    }
    static instance = new DuckDBAnyType();
    static create(alias) {
        return alias ? new DuckDBAnyType(alias) : DuckDBAnyType.instance;
    }
}
exports.DuckDBAnyType = DuckDBAnyType;
exports.ANY = DuckDBAnyType.instance;
class DuckDBBigNumType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.BIGNUM, alias);
    }
    static instance = new DuckDBBigNumType();
    static create(alias) {
        return alias ? new DuckDBBigNumType(alias) : DuckDBBigNumType.instance;
    }
    static Max = 179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368n;
    static Min = -179769313486231570814527423731704356798070567525844996598917476803157260780028538760589558632766878171540458953514382464234321326889464182768467546703537516986049910576551282076245490090389328944075868508455133942304583236903222948165808559332123348274797826204144723168738177180919299881250404026184124858368n;
    get max() {
        return DuckDBBigNumType.Max;
    }
    get min() {
        return DuckDBBigNumType.Min;
    }
}
exports.DuckDBBigNumType = DuckDBBigNumType;
exports.BIGNUM = DuckDBBigNumType.instance;
class DuckDBSQLNullType extends BaseDuckDBType {
    constructor(alias) {
        super(DuckDBTypeId_1.DuckDBTypeId.SQLNULL, alias);
    }
    static instance = new DuckDBSQLNullType();
    static create(alias) {
        return alias ? new DuckDBSQLNullType(alias) : DuckDBSQLNullType.instance;
    }
}
exports.DuckDBSQLNullType = DuckDBSQLNullType;
exports.SQLNULL = DuckDBSQLNullType.instance;
