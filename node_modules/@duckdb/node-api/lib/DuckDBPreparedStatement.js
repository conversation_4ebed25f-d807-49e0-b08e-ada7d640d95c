"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBPreparedStatement = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const createValue_1 = require("./createValue");
const DuckDBLogicalType_1 = require("./DuckDBLogicalType");
const DuckDBMaterializedResult_1 = require("./DuckDBMaterializedResult");
const DuckDBPendingResult_1 = require("./DuckDBPendingResult");
const DuckDBResult_1 = require("./DuckDBResult");
const DuckDBResultReader_1 = require("./DuckDBResultReader");
const DuckDBType_1 = require("./DuckDBType");
const typeForValue_1 = require("./typeForValue");
const values_1 = require("./values");
class DuckDBPreparedStatement {
    prepared_statement;
    constructor(prepared_statement) {
        this.prepared_statement = prepared_statement;
    }
    destroySync() {
        return node_bindings_1.default.destroy_prepare_sync(this.prepared_statement);
    }
    get statementType() {
        return node_bindings_1.default.prepared_statement_type(this.prepared_statement);
    }
    get parameterCount() {
        return node_bindings_1.default.nparams(this.prepared_statement);
    }
    parameterName(parameterIndex) {
        return node_bindings_1.default.parameter_name(this.prepared_statement, parameterIndex);
    }
    parameterTypeId(parameterIndex) {
        return node_bindings_1.default.param_type(this.prepared_statement, parameterIndex);
    }
    parameterType(parameterIndex) {
        return DuckDBLogicalType_1.DuckDBLogicalType.create(node_bindings_1.default.param_logical_type(this.prepared_statement, parameterIndex)).asType();
    }
    clearBindings() {
        node_bindings_1.default.clear_bindings(this.prepared_statement);
    }
    parameterIndex(parameterName) {
        return node_bindings_1.default.bind_parameter_index(this.prepared_statement, parameterName);
    }
    bindBoolean(parameterIndex, value) {
        node_bindings_1.default.bind_boolean(this.prepared_statement, parameterIndex, value);
    }
    bindTinyInt(parameterIndex, value) {
        node_bindings_1.default.bind_int8(this.prepared_statement, parameterIndex, value);
    }
    bindSmallInt(parameterIndex, value) {
        node_bindings_1.default.bind_int16(this.prepared_statement, parameterIndex, value);
    }
    bindInteger(parameterIndex, value) {
        node_bindings_1.default.bind_int32(this.prepared_statement, parameterIndex, value);
    }
    bindBigInt(parameterIndex, value) {
        node_bindings_1.default.bind_int64(this.prepared_statement, parameterIndex, value);
    }
    bindHugeInt(parameterIndex, value) {
        node_bindings_1.default.bind_hugeint(this.prepared_statement, parameterIndex, value);
    }
    bindUTinyInt(parameterIndex, value) {
        node_bindings_1.default.bind_uint8(this.prepared_statement, parameterIndex, value);
    }
    bindUSmallInt(parameterIndex, value) {
        node_bindings_1.default.bind_uint16(this.prepared_statement, parameterIndex, value);
    }
    bindUInteger(parameterIndex, value) {
        node_bindings_1.default.bind_uint32(this.prepared_statement, parameterIndex, value);
    }
    bindUBigInt(parameterIndex, value) {
        node_bindings_1.default.bind_uint64(this.prepared_statement, parameterIndex, value);
    }
    bindUHugeInt(parameterIndex, value) {
        node_bindings_1.default.bind_uhugeint(this.prepared_statement, parameterIndex, value);
    }
    bindBigNum(parameterIndex, value) {
        this.bindValue(parameterIndex, value, DuckDBType_1.BIGNUM);
    }
    bindDecimal(parameterIndex, value) {
        node_bindings_1.default.bind_decimal(this.prepared_statement, parameterIndex, value);
    }
    bindFloat(parameterIndex, value) {
        node_bindings_1.default.bind_float(this.prepared_statement, parameterIndex, value);
    }
    bindDouble(parameterIndex, value) {
        node_bindings_1.default.bind_double(this.prepared_statement, parameterIndex, value);
    }
    bindDate(parameterIndex, value) {
        node_bindings_1.default.bind_date(this.prepared_statement, parameterIndex, value);
    }
    bindTime(parameterIndex, value) {
        node_bindings_1.default.bind_time(this.prepared_statement, parameterIndex, value);
    }
    bindTimeTZ(parameterIndex, value) {
        this.bindValue(parameterIndex, value, DuckDBType_1.TIMETZ);
    }
    bindTimestamp(parameterIndex, value) {
        node_bindings_1.default.bind_timestamp(this.prepared_statement, parameterIndex, value);
    }
    bindTimestampTZ(parameterIndex, value) {
        this.bindValue(parameterIndex, value, DuckDBType_1.TIMESTAMPTZ);
    }
    bindTimestampSeconds(parameterIndex, value) {
        this.bindValue(parameterIndex, value, DuckDBType_1.TIMESTAMP_S);
    }
    bindTimestampMilliseconds(parameterIndex, value) {
        this.bindValue(parameterIndex, value, DuckDBType_1.TIMESTAMP_MS);
    }
    bindTimestampNanoseconds(parameterIndex, value) {
        this.bindValue(parameterIndex, value, DuckDBType_1.TIMESTAMP_NS);
    }
    bindInterval(parameterIndex, value) {
        node_bindings_1.default.bind_interval(this.prepared_statement, parameterIndex, value);
    }
    bindVarchar(parameterIndex, value) {
        node_bindings_1.default.bind_varchar(this.prepared_statement, parameterIndex, value);
    }
    bindBlob(parameterIndex, value) {
        node_bindings_1.default.bind_blob(this.prepared_statement, parameterIndex, value);
    }
    bindEnum(parameterIndex, value, type) {
        this.bindValue(parameterIndex, value, type);
    }
    bindArray(parameterIndex, value, type) {
        this.bindValue(parameterIndex, value instanceof values_1.DuckDBArrayValue ? value : (0, values_1.arrayValue)(value), type);
    }
    bindList(parameterIndex, value, type) {
        this.bindValue(parameterIndex, value instanceof values_1.DuckDBListValue ? value : (0, values_1.listValue)(value), type);
    }
    bindStruct(parameterIndex, value, type) {
        this.bindValue(parameterIndex, value instanceof values_1.DuckDBStructValue ? value : (0, values_1.structValue)(value), type);
    }
    bindMap(parameterIndex, value, type) {
        this.bindValue(parameterIndex, value, type);
    }
    bindUnion(parameterIndex, value, type) {
        this.bindValue(parameterIndex, value, type);
    }
    bindUUID(parameterIndex, value) {
        this.bindValue(parameterIndex, value, DuckDBType_1.UUID);
    }
    bindBit(parameterIndex, value) {
        this.bindValue(parameterIndex, value, DuckDBType_1.BIT);
    }
    bindNull(parameterIndex) {
        node_bindings_1.default.bind_null(this.prepared_statement, parameterIndex);
    }
    bindValue(parameterIndex, value, type) {
        node_bindings_1.default.bind_value(this.prepared_statement, parameterIndex, (0, createValue_1.createValue)(type ? type : (0, typeForValue_1.typeForValue)(value), value));
    }
    bind(values, types) {
        if (Array.isArray(values)) {
            const typesIsArray = Array.isArray(types);
            for (let i = 0; i < values.length; i++) {
                this.bindValue(i + 1, values[i], typesIsArray ? types[i] : undefined);
            }
        }
        else {
            const typesIsRecord = types && !Array.isArray(types);
            for (const key in values) {
                this.bindValue(this.parameterIndex(key), values[key], typesIsRecord ? types[key] : undefined);
            }
        }
    }
    async run() {
        return new DuckDBMaterializedResult_1.DuckDBMaterializedResult(await node_bindings_1.default.execute_prepared(this.prepared_statement));
    }
    async runAndRead() {
        return new DuckDBResultReader_1.DuckDBResultReader(await this.run());
    }
    async runAndReadAll() {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.run());
        await reader.readAll();
        return reader;
    }
    async runAndReadUntil(targetRowCount) {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.run());
        await reader.readUntil(targetRowCount);
        return reader;
    }
    async stream() {
        return new DuckDBResult_1.DuckDBResult(await node_bindings_1.default.execute_prepared_streaming(this.prepared_statement));
    }
    async streamAndRead() {
        return new DuckDBResultReader_1.DuckDBResultReader(await this.stream());
    }
    async streamAndReadAll() {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.stream());
        await reader.readAll();
        return reader;
    }
    async streamAndReadUntil(targetRowCount) {
        const reader = new DuckDBResultReader_1.DuckDBResultReader(await this.stream());
        await reader.readUntil(targetRowCount);
        return reader;
    }
    start() {
        return new DuckDBPendingResult_1.DuckDBPendingResult(node_bindings_1.default.pending_prepared(this.prepared_statement));
    }
    startStream() {
        return new DuckDBPendingResult_1.DuckDBPendingResult(node_bindings_1.default.pending_prepared_streaming(this.prepared_statement));
    }
}
exports.DuckDBPreparedStatement = DuckDBPreparedStatement;
