"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.configurationOptionDescriptions = configurationOptionDescriptions;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
function configurationOptionDescriptions() {
    const descriptions = {};
    const count = node_bindings_1.default.config_count();
    for (let i = 0; i < count; i++) {
        const { name, description } = node_bindings_1.default.get_config_flag(i);
        descriptions[name] = description;
    }
    return descriptions;
}
