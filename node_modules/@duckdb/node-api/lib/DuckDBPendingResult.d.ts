import duckdb from '@duckdb/node-bindings';
import { Duck<PERSON><PERSON><PERSON>ult } from './DuckDBResult';
import { DuckDBResultReader } from './DuckDBResultReader';
export declare enum DuckDBPendingResultState {
    RESULT_READY = 0,
    RESULT_NOT_READY = 1,
    NO_TASKS_AVAILABLE = 3
}
export declare class DuckDBPendingResult {
    private readonly pending_result;
    constructor(pending_result: duckdb.PendingResult);
    runTask(): DuckDBPendingResultState;
    getResult(): Promise<DuckDBResult>;
    read(): Promise<DuckDBResultReader>;
    readAll(): Promise<DuckDBResultReader>;
    readUntil(targetRowCount: number): Promise<DuckDBResultReader>;
}
