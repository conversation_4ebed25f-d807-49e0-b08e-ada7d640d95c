"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createResult = createResult;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBMaterializedResult_1 = require("./DuckDBMaterializedResult");
const DuckDBResult_1 = require("./DuckDBResult");
function createResult(result) {
    if (node_bindings_1.default.result_is_streaming(result)) {
        return new DuckDBResult_1.DuckDBResult(result);
    }
    else {
        return new DuckDBMaterializedResult_1.DuckDBMaterializedResult(result);
    }
}
