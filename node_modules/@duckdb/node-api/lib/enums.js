"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatementType = exports.ResultReturnType = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
exports.ResultReturnType = node_bindings_1.default.ResultType;
exports.StatementType = node_bindings_1.default.StatementType;
