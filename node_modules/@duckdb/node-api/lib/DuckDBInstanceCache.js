"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBInstanceCache = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBInstance_1 = require("./DuckDBInstance");
const createConfig_1 = require("./createConfig");
class DuckDBInstanceCache {
    cache;
    constructor() {
        this.cache = node_bindings_1.default.create_instance_cache();
    }
    async getOrCreateInstance(path, options) {
        const config = (0, createConfig_1.createConfig)(options);
        const db = await node_bindings_1.default.get_or_create_from_cache(this.cache, path, config);
        return new DuckDBInstance_1.DuckDBInstance(db);
    }
    static singletonInstance;
    static get singleton() {
        if (!DuckDBInstanceCache.singletonInstance) {
            DuckDBInstanceCache.singletonInstance = new DuckDBInstanceCache();
        }
        return DuckDBInstanceCache.singletonInstance;
    }
}
exports.DuckDBInstanceCache = DuckDBInstanceCache;
