"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBMaterializedResult = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBDataChunk_1 = require("./DuckDBDataChunk");
const DuckDBResult_1 = require("./DuckDBResult");
class DuckDBMaterializedResult extends DuckDBResult_1.DuckDBResult {
    constructor(result) {
        super(result);
    }
    get rowCount() {
        return node_bindings_1.default.row_count(this.result);
    }
    get chunkCount() {
        return node_bindings_1.default.result_chunk_count(this.result);
    }
    getChunk(chunkIndex) {
        return new DuckDBDataChunk_1.DuckDBDataChunk(node_bindings_1.default.result_get_chunk(this.result, chunkIndex));
    }
}
exports.DuckDBMaterializedResult = DuckDBMaterializedResult;
