"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBDataChunk = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBVector_1 = require("./DuckDBVector");
class DuckDBDataChunk {
    chunk;
    vectors = [];
    constructor(chunk) {
        this.chunk = chunk;
    }
    static create(types, rowCount) {
        const chunk = new DuckDBDataChunk(node_bindings_1.default.create_data_chunk(types.map((t) => t.toLogicalType().logical_type)));
        if (rowCount != undefined) {
            chunk.rowCount = rowCount;
        }
        return chunk;
    }
    reset() {
        node_bindings_1.default.data_chunk_reset(this.chunk);
    }
    get columnCount() {
        return node_bindings_1.default.data_chunk_get_column_count(this.chunk);
    }
    get rowCount() {
        return node_bindings_1.default.data_chunk_get_size(this.chunk);
    }
    set rowCount(count) {
        const maxRowCount = node_bindings_1.default.vector_size();
        if (count > maxRowCount) {
            throw new Error(`A data chunk cannot have more than ${maxRowCount} rows`);
        }
        node_bindings_1.default.data_chunk_set_size(this.chunk, count);
    }
    getColumnVector(columnIndex) {
        if (this.vectors[columnIndex]) {
            return this.vectors[columnIndex];
        }
        const vector = DuckDBVector_1.DuckDBVector.create(node_bindings_1.default.data_chunk_get_vector(this.chunk, columnIndex), this.rowCount);
        this.vectors[columnIndex] = vector;
        return vector;
    }
    visitColumnValues(columnIndex, visitValue) {
        const vector = this.getColumnVector(columnIndex);
        const type = vector.type;
        for (let rowIndex = 0; rowIndex < vector.itemCount; rowIndex++) {
            visitValue(vector.getItem(rowIndex), rowIndex, columnIndex, type);
        }
    }
    appendColumnValues(columnIndex, values) {
        this.visitColumnValues(columnIndex, (value) => values.push(value));
    }
    getColumnValues(columnIndex) {
        const values = [];
        this.appendColumnValues(columnIndex, values);
        return values;
    }
    convertColumnValues(columnIndex, converter) {
        const convertedValues = [];
        this.visitColumnValues(columnIndex, (value, _r, _c, type) => convertedValues.push(converter(value, type, converter)));
        return convertedValues;
    }
    setColumnValues(columnIndex, values) {
        const vector = this.getColumnVector(columnIndex);
        if (vector.itemCount !== values.length) {
            throw new Error(`number of values must equal chunk row count`);
        }
        for (let i = 0; i < values.length; i++) {
            vector.setItem(i, values[i]);
        }
        vector.flush();
    }
    visitColumns(visitColumn) {
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            visitColumn(this.getColumnValues(columnIndex), columnIndex, this.getColumnVector(columnIndex).type);
        }
    }
    appendToColumns(columns) {
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            let column = columns[columnIndex];
            if (!column) {
                column = [];
                columns[columnIndex] = column;
            }
            this.appendColumnValues(columnIndex, column);
        }
    }
    getColumns() {
        const columns = [];
        this.visitColumns((column) => columns.push(column));
        return columns;
    }
    convertColumns(converter) {
        const convertedColumns = [];
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            convertedColumns.push(this.convertColumnValues(columnIndex, converter));
        }
        return convertedColumns;
    }
    setColumns(columns) {
        if (columns.length > 0) {
            this.rowCount = columns[0].length;
        }
        for (let columnIndex = 0; columnIndex < columns.length; columnIndex++) {
            this.setColumnValues(columnIndex, columns[columnIndex]);
        }
    }
    appendToColumnsObject(columnNames, columnsObject) {
        const columnCount = this.columnCount;
        if (columnNames.length !== columnCount) {
            throw new Error(`Provided number of column names (${columnNames.length}) does not match column count (${this.columnCount})`);
        }
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            const columnName = columnNames[columnIndex];
            let columnValues = columnsObject[columnName];
            if (!columnValues) {
                columnValues = [];
                columnsObject[columnName] = columnValues;
            }
            this.appendColumnValues(columnIndex, columnValues);
        }
    }
    getColumnsObject(columnNames) {
        const columnsObject = {};
        this.appendToColumnsObject(columnNames, columnsObject);
        return columnsObject;
    }
    visitColumnMajor(visitValue) {
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            this.visitColumnValues(columnIndex, visitValue);
        }
    }
    visitRowValues(rowIndex, visitValue) {
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            const vector = this.getColumnVector(columnIndex);
            visitValue(vector.getItem(rowIndex), rowIndex, columnIndex, vector.type);
        }
    }
    appendRowValues(rowIndex, values) {
        this.visitRowValues(rowIndex, (value) => values.push(value));
    }
    getRowValues(rowIndex) {
        const values = [];
        this.appendRowValues(rowIndex, values);
        return values;
    }
    convertRowValues(rowIndex, converter) {
        const convertedValues = [];
        this.visitRowValues(rowIndex, (value, _, columnIndex) => convertedValues.push(converter(value, this.getColumnVector(columnIndex).type, converter)));
        return convertedValues;
    }
    visitRows(visitRow) {
        const rowCount = this.rowCount;
        for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
            visitRow(this.getRowValues(rowIndex), rowIndex);
        }
    }
    appendToRows(rows) {
        this.visitRows((row) => rows.push(row));
    }
    getRows() {
        const rows = [];
        this.appendToRows(rows);
        return rows;
    }
    convertRows(converter) {
        const convertedRows = [];
        const rowCount = this.rowCount;
        for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
            convertedRows.push(this.convertRowValues(rowIndex, converter));
        }
        return convertedRows;
    }
    setRows(rows) {
        this.rowCount = rows.length;
        const columnCount = this.columnCount;
        for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            const vector = this.getColumnVector(columnIndex);
            for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
                vector.setItem(rowIndex, rows[rowIndex][columnIndex]);
            }
            vector.flush();
        }
    }
    appendToRowObjects(columnNames, rowObjects) {
        const columnCount = this.columnCount;
        if (columnNames.length !== columnCount) {
            throw new Error(`Provided number of column names (${columnNames.length}) does not match column count (${this.columnCount})`);
        }
        const rowCount = this.rowCount;
        for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
            let rowObject = {};
            this.visitRowValues(rowIndex, (value, _, columnIndex) => {
                rowObject[columnNames[columnIndex]] = value;
            });
            rowObjects.push(rowObject);
        }
    }
    getRowObjects(columnNames) {
        const rowObjects = [];
        this.appendToRowObjects(columnNames, rowObjects);
        return rowObjects;
    }
    visitRowMajor(visitValue) {
        const rowCount = this.rowCount;
        const columnCount = this.columnCount;
        for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
            for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
                const vector = this.getColumnVector(columnIndex);
                visitValue(vector.getItem(rowIndex), rowIndex, columnIndex, vector.type);
            }
        }
    }
}
exports.DuckDBDataChunk = DuckDBDataChunk;
