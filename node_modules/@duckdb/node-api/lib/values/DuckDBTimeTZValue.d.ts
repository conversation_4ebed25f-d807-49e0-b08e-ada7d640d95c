import { TimeTZ, TimeTZParts } from '@duckdb/node-bindings';
export type { TimeTZParts };
export declare class DuckDBTimeTZValue implements TimeTZ {
    /**
     * 40 bits for micros, then 24 bits for encoded offset in seconds.
     *
     * Max absolute unencoded offset = 15:59:59 = 60 * (60 * 15 + 59) + 59 = 57599.
     *
     * Encoded offset is unencoded offset inverted then shifted (by +57599) to unsigned.
     *
     * Max unencoded offset = 57599 -> -57599 -> 0 encoded.
     *
     * Min unencoded offset = -57599 -> 57599 -> 115198 encoded.
     */
    readonly bits: bigint;
    /** Ranges from 0 to 86400000000 (= 24 * 60 * 60 * 1000 * 1000) */
    readonly micros: bigint;
    /** In seconds, ranges from -57599 to 57599 (= 16 * 60 * 60 - 1) */
    readonly offset: number;
    constructor(bits: bigint, micros: bigint, offset: number);
    toString(): string;
    toParts(): TimeTZParts;
    static TimeBits: number;
    static OffsetBits: number;
    static MaxOffset: number;
    static MinOffset: number;
    static MaxMicros: bigint;
    static MinMicros: bigint;
    static fromBits(bits: bigint): DuckDBTimeTZValue;
    static fromMicrosAndOffset(micros: bigint, offset: number): DuckDBTimeTZValue;
    static fromParts(parts: TimeTZParts): DuckDBTimeTZValue;
    static readonly Max: DuckDBTimeTZValue;
    static readonly Min: DuckDBTimeTZValue;
}
export declare function timeTZValue(micros: bigint, offset: number): DuckDBTimeTZValue;
