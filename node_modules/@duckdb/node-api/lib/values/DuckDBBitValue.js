"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBBitValue = void 0;
exports.bitValue = bitValue;
class DuckDBBitValue {
    data;
    constructor(data) {
        this.data = data;
    }
    padding() {
        return this.data[0];
    }
    get length() {
        return (this.data.length - 1) * 8 - this.padding();
    }
    getBool(index) {
        const offset = index + this.padding();
        const dataIndex = Math.floor(offset / 8) + 1;
        const byte = this.data[dataIndex] >> (7 - (offset % 8));
        return (byte & 1) !== 0;
    }
    toBools() {
        const bools = [];
        const length = this.length;
        for (let i = 0; i < length; i++) {
            bools.push(this.getBool(i));
        }
        return bools;
    }
    getBit(index) {
        return this.getBool(index) ? 1 : 0;
    }
    toBits() {
        const bits = [];
        const length = this.length;
        for (let i = 0; i < length; i++) {
            bits.push(this.getBit(i));
        }
        return bits;
    }
    toString() {
        const length = this.length;
        const chars = Array.from({ length });
        for (let i = 0; i < length; i++) {
            chars[i] = this.getBool(i) ? '1' : '0';
        }
        return chars.join('');
    }
    static fromString(str, on = '1') {
        return DuckDBBitValue.fromLengthAndPredicate(str.length, i => str[i] === on);
    }
    static fromBits(bits, on = 1) {
        return DuckDBBitValue.fromLengthAndPredicate(bits.length, i => bits[i] === on);
    }
    static fromBools(bools) {
        return DuckDBBitValue.fromLengthAndPredicate(bools.length, i => bools[i]);
    }
    static fromLengthAndPredicate(length, predicate) {
        const byteCount = Math.ceil(length / 8) + 1;
        const paddingBitCount = (8 - (length % 8)) % 8;
        const data = new Uint8Array(byteCount);
        let byteIndex = 0;
        // first byte contains count of padding bits
        data[byteIndex++] = paddingBitCount;
        let byte = 0;
        let byteBit = 0;
        // padding consists of 1s in MSB of second byte
        while (byteBit < paddingBitCount) {
            byte <<= 1;
            byte |= 1;
            byteBit++;
        }
        let bitIndex = 0;
        while (byteIndex < byteCount) {
            while (byteBit < 8) {
                byte <<= 1;
                if (predicate(bitIndex++)) {
                    byte |= 1;
                }
                byteBit++;
            }
            data[byteIndex++] = byte;
            byte = 0;
            byteBit = 0;
        }
        return new DuckDBBitValue(data);
    }
}
exports.DuckDBBitValue = DuckDBBitValue;
function bitValue(input) {
    if (typeof input === 'string') {
        return DuckDBBitValue.fromString(input);
    }
    if (input.length > 0) {
        if (typeof input[0] === 'boolean') {
            return DuckDBBitValue.fromBools(input);
        }
        else if (typeof input[0] === 'number') {
            return DuckDBBitValue.fromBits(input);
        }
    }
    return DuckDBBitValue.fromLengthAndPredicate(0, _ => false);
}
