"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBDecimalValue = void 0;
exports.decimalValue = decimalValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const stringFromDecimal_1 = require("../conversion/stringFromDecimal");
class DuckDBDecimalValue {
    /** Total number of decimal digits (including fractional digits) in represented number. */
    width;
    /** Number of fractional digits in represented number. */
    scale;
    /** Scaled-up value. Represented number is value/(10^scale). */
    value;
    /**
     * @param value Scaled-up value. Represented number is value/(10^scale).
     * @param width Total number of decimal digits (including fractional digits) in represented number.
     * @param scale Number of fractional digits in represented number.
     */
    constructor(value, width, scale) {
        this.width = width;
        this.scale = scale;
        this.value = value;
    }
    toString() {
        return (0, stringFromDecimal_1.stringFromDecimal)(this.value, this.scale);
    }
    toDouble() {
        return node_bindings_1.default.decimal_to_double(this);
    }
    static fromDouble(double, width, scale) {
        const decimal = node_bindings_1.default.double_to_decimal(double, width, scale);
        return new DuckDBDecimalValue(decimal.value, decimal.width, decimal.scale);
    }
}
exports.DuckDBDecimalValue = DuckDBDecimalValue;
function decimalValue(value, width, scale) {
    if (typeof value === 'number') {
        return DuckDBDecimalValue.fromDouble(value, width, scale);
    }
    return new DuckDBDecimalValue(value, width, scale);
}
