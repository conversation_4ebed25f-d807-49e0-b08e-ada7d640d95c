"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBTimestampMicrosecondsValue = exports.DuckDBTimestampValue = void 0;
exports.timestampValue = timestampValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const dateTimeStringConversion_1 = require("../conversion/dateTimeStringConversion");
const DuckDBTimestampMillisecondsValue_1 = require("./DuckDBTimestampMillisecondsValue");
class DuckDBTimestampValue {
    micros;
    constructor(micros) {
        this.micros = micros;
    }
    get isFinite() {
        return node_bindings_1.default.is_finite_timestamp(this);
    }
    toString() {
        return (0, dateTimeStringConversion_1.getDuckDBTimestampStringFromMicroseconds)(this.micros);
    }
    toParts() {
        return node_bindings_1.default.from_timestamp(this);
    }
    static fromParts(parts) {
        return new DuckDBTimestampValue(node_bindings_1.default.to_timestamp(parts).micros);
    }
    static Epoch = new DuckDBTimestampValue(0n);
    static Max = new DuckDBTimestampValue(2n ** 63n - 2n);
    static Min = new DuckDBTimestampValue(DuckDBTimestampMillisecondsValue_1.DuckDBTimestampMillisecondsValue.Min.millis * 1000n);
    static PosInf = new DuckDBTimestampValue(2n ** 63n - 1n);
    static NegInf = new DuckDBTimestampValue(-(2n ** 63n - 1n));
}
exports.DuckDBTimestampValue = DuckDBTimestampValue;
exports.DuckDBTimestampMicrosecondsValue = DuckDBTimestampValue;
function timestampValue(microsOrParts) {
    if (typeof microsOrParts === 'bigint') {
        return new DuckDBTimestampValue(microsOrParts);
    }
    return DuckDBTimestampValue.fromParts(microsOrParts);
}
