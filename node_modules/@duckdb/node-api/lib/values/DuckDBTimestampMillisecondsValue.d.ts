import { TimestampMilliseconds } from '@duckdb/node-bindings';
export declare class DuckDBTimestampMillisecondsValue implements TimestampMilliseconds {
    readonly millis: bigint;
    constructor(millis: bigint);
    get isFinite(): boolean;
    toString(): string;
    static readonly Epoch: DuckDBTimestampMillisecondsValue;
    static readonly Max: DuckDBTimestampMillisecondsValue;
    static readonly Min: DuckDBTimestampMillisecondsValue;
    static readonly PosInf: DuckDBTimestampMillisecondsValue;
    static readonly NegInf: DuckDBTimestampMillisecondsValue;
}
export declare function timestampMillisValue(millis: bigint): DuckDBTimestampMillisecondsValue;
