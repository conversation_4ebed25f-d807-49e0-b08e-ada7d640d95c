"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBTimeValue = void 0;
exports.timeValue = timeValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const dateTimeStringConversion_1 = require("../conversion/dateTimeStringConversion");
class DuckDBTimeValue {
    micros;
    constructor(micros) {
        this.micros = micros;
    }
    toString() {
        return (0, dateTimeStringConversion_1.getDuckDBTimeStringFromMicrosecondsInDay)(this.micros);
    }
    toParts() {
        return node_bindings_1.default.from_time(this);
    }
    static fromParts(parts) {
        return new DuckDBTimeValue(node_bindings_1.default.to_time(parts).micros);
    }
    static Max = new DuckDBTimeValue(24n * 60n * 60n * 1000n * 1000n);
    static Min = new DuckDBTimeValue(0n);
}
exports.DuckDBTimeValue = DuckDBTimeValue;
function timeValue(microsOrParts) {
    if (typeof microsOrParts === 'bigint') {
        return new DuckDBTimeValue(microsOrParts);
    }
    return DuckDBTimeValue.fromParts(microsOrParts);
}
