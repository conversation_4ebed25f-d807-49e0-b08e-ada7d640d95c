import { Decimal } from '@duckdb/node-bindings';
export declare class DuckDBDecimalValue implements Decimal {
    /** Total number of decimal digits (including fractional digits) in represented number. */
    readonly width: number;
    /** Number of fractional digits in represented number. */
    readonly scale: number;
    /** Scaled-up value. Represented number is value/(10^scale). */
    readonly value: bigint;
    /**
     * @param value Scaled-up value. Represented number is value/(10^scale).
     * @param width Total number of decimal digits (including fractional digits) in represented number.
     * @param scale Number of fractional digits in represented number.
     */
    constructor(value: bigint, width: number, scale: number);
    toString(): string;
    toDouble(): number;
    static fromDouble(double: number, width: number, scale: number): DuckDBDecimalValue;
}
export declare function decimalValue(value: bigint | number, width: number, scale: number): DuckDBDecimalValue;
