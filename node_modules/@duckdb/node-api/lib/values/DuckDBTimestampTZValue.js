"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBTimestampTZValue = void 0;
exports.timestampTZValue = timestampTZValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const dateTimeStringConversion_1 = require("../conversion/dateTimeStringConversion");
const DuckDBTimestampValue_1 = require("./DuckDBTimestampValue");
class DuckDBTimestampTZValue {
    static timezoneOffsetInMinutes = -new Date().getTimezoneOffset();
    micros;
    constructor(micros) {
        this.micros = micros;
    }
    get isFinite() {
        return node_bindings_1.default.is_finite_timestamp(this);
    }
    toString() {
        return (0, dateTimeStringConversion_1.getDuckDBTimestampStringFromMicroseconds)(this.micros, DuckDBTimestampTZValue.timezoneOffsetInMinutes);
    }
    toParts() {
        return node_bindings_1.default.from_timestamp(this);
    }
    static fromParts(parts) {
        return new DuckDBTimestampTZValue(node_bindings_1.default.to_timestamp(parts).micros);
    }
    static Epoch = new DuckDBTimestampTZValue(0n);
    static Max = new DuckDBTimestampTZValue(DuckDBTimestampValue_1.DuckDBTimestampValue.Max.micros);
    static Min = new DuckDBTimestampTZValue(DuckDBTimestampValue_1.DuckDBTimestampValue.Min.micros);
    static PosInf = new DuckDBTimestampTZValue(DuckDBTimestampValue_1.DuckDBTimestampValue.PosInf.micros);
    static NegInf = new DuckDBTimestampTZValue(DuckDBTimestampValue_1.DuckDBTimestampValue.NegInf.micros);
}
exports.DuckDBTimestampTZValue = DuckDBTimestampTZValue;
function timestampTZValue(microsOrParts) {
    if (typeof microsOrParts === 'bigint') {
        return new DuckDBTimestampTZValue(microsOrParts);
    }
    return DuckDBTimestampTZValue.fromParts(microsOrParts);
}
