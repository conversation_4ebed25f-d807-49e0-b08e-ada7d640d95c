"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBTimestampNanosecondsValue = void 0;
exports.timestampNanosValue = timestampNanosValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const dateTimeStringConversion_1 = require("../conversion/dateTimeStringConversion");
class DuckDBTimestampNanosecondsValue {
    nanos;
    constructor(nanos) {
        this.nanos = nanos;
    }
    get isFinite() {
        return node_bindings_1.default.is_finite_timestamp_ns(this);
    }
    toString() {
        return (0, dateTimeStringConversion_1.getDuckDBTimestampStringFromNanoseconds)(this.nanos);
    }
    static Epoch = new DuckDBTimestampNanosecondsValue(0n);
    static Max = new DuckDBTimestampNanosecondsValue(2n ** 63n - 2n);
    static Min = new DuckDBTimestampNanosecondsValue(-9223286400000000000n);
    static PosInf = new DuckDBTimestampNanosecondsValue(2n ** 63n - 1n);
    static NegInf = new DuckDBTimestampNanosecondsValue(-(2n ** 63n - 1n));
}
exports.DuckDBTimestampNanosecondsValue = DuckDBTimestampNanosecondsValue;
function timestampNanosValue(nanos) {
    return new DuckDBTimestampNanosecondsValue(nanos);
}
