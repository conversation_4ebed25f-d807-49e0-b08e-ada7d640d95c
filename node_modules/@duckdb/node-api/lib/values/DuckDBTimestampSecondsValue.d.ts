import { TimestampSeconds } from '@duckdb/node-bindings';
export declare class DuckDBTimestampSecondsValue implements TimestampSeconds {
    readonly seconds: bigint;
    constructor(seconds: bigint);
    get isFinite(): boolean;
    toString(): string;
    static readonly Epoch: DuckDBTimestampSecondsValue;
    static readonly Max: DuckDBTimestampSecondsValue;
    static readonly Min: DuckDBTimestampSecondsValue;
    static readonly PosInf: DuckDBTimestampSecondsValue;
    static readonly NegInf: DuckDBTimestampSecondsValue;
}
export declare function timestampSecondsValue(seconds: bigint): DuckDBTimestampSecondsValue;
