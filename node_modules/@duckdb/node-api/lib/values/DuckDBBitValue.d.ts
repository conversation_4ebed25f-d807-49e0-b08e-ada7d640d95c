export declare class DuckDBBitValue {
    readonly data: Uint8Array;
    constructor(data: Uint8Array);
    padding(): number;
    get length(): number;
    getBool(index: number): boolean;
    toBools(): boolean[];
    getBit(index: number): 0 | 1;
    toBits(): number[];
    toString(): string;
    static fromString(str: string, on?: string): DuckDBBitValue;
    static fromBits(bits: readonly number[], on?: number): DuckDBBitValue;
    static fromBools(bools: readonly boolean[]): DuckDBBitValue;
    static fromLengthAndPredicate(length: number, predicate: (index: number) => boolean): DuckDBBitValue;
}
export declare function bitValue(input: string | readonly boolean[] | readonly number[]): DuckDBBitValue;
