import { Time, TimeParts } from '@duckdb/node-bindings';
export type { TimeParts };
export declare class DuckDBTimeValue implements Time {
    readonly micros: bigint;
    constructor(micros: bigint);
    toString(): string;
    toParts(): TimeParts;
    static fromParts(parts: TimeParts): DuckDBTimeValue;
    static readonly Max: DuckDBTimeValue;
    static readonly Min: DuckDBTimeValue;
}
export declare function timeValue(microsOrParts: bigint | TimeParts): DuckDBTimeValue;
