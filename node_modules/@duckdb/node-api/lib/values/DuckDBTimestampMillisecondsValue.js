"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBTimestampMillisecondsValue = void 0;
exports.timestampMillisValue = timestampMillisValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const dateTimeStringConversion_1 = require("../conversion/dateTimeStringConversion");
const DuckDBTimestampSecondsValue_1 = require("./DuckDBTimestampSecondsValue");
class DuckDBTimestampMillisecondsValue {
    millis;
    constructor(millis) {
        this.millis = millis;
    }
    get isFinite() {
        return node_bindings_1.default.is_finite_timestamp_ms(this);
    }
    toString() {
        return (0, dateTimeStringConversion_1.getDuckDBTimestampStringFromMilliseconds)(this.millis);
    }
    static Epoch = new DuckDBTimestampMillisecondsValue(0n);
    static Max = new DuckDBTimestampMillisecondsValue((2n ** 63n - 2n) / 1000n);
    static Min = new DuckDBTimestampMillisecondsValue(DuckDBTimestampSecondsValue_1.DuckDBTimestampSecondsValue.Min.seconds * 1000n);
    static PosInf = new DuckDBTimestampMillisecondsValue(2n ** 63n - 1n);
    static NegInf = new DuckDBTimestampMillisecondsValue(-(2n ** 63n - 1n));
}
exports.DuckDBTimestampMillisecondsValue = DuckDBTimestampMillisecondsValue;
function timestampMillisValue(millis) {
    return new DuckDBTimestampMillisecondsValue(millis);
}
