"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBTimeTZValue = void 0;
exports.timeTZValue = timeTZValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const dateTimeStringConversion_1 = require("../conversion/dateTimeStringConversion");
class DuckDBTimeTZValue {
    /**
     * 40 bits for micros, then 24 bits for encoded offset in seconds.
     *
     * Max absolute unencoded offset = 15:59:59 = 60 * (60 * 15 + 59) + 59 = 57599.
     *
     * Encoded offset is unencoded offset inverted then shifted (by +57599) to unsigned.
     *
     * Max unencoded offset = 57599 -> -57599 -> 0 encoded.
     *
     * Min unencoded offset = -57599 -> 57599 -> 115198 encoded.
     */
    bits;
    /** Ranges from 0 to 86400000000 (= 24 * 60 * 60 * 1000 * 1000) */
    micros;
    /** In seconds, ranges from -57599 to 57599 (= 16 * 60 * 60 - 1) */
    offset;
    constructor(bits, micros, offset) {
        this.bits = bits;
        this.micros = micros;
        this.offset = offset;
    }
    toString() {
        return `${(0, dateTimeStringConversion_1.getDuckDBTimeStringFromMicrosecondsInDay)(this.micros)}${(0, dateTimeStringConversion_1.getOffsetStringFromSeconds)(this.offset)}`;
    }
    toParts() {
        return node_bindings_1.default.from_time_tz(this);
    }
    static TimeBits = 40;
    static OffsetBits = 24;
    static MaxOffset = 16 * 60 * 60 - 1; // ±15:59:59 = 57599 seconds
    static MinOffset = -DuckDBTimeTZValue.MaxOffset;
    static MaxMicros = 24n * 60n * 60n * 1000n * 1000n; // 86400000000
    static MinMicros = 0n;
    static fromBits(bits) {
        const micros = BigInt.asUintN(DuckDBTimeTZValue.TimeBits, bits >> BigInt(DuckDBTimeTZValue.OffsetBits));
        const offset = DuckDBTimeTZValue.MaxOffset -
            Number(BigInt.asUintN(DuckDBTimeTZValue.OffsetBits, bits));
        return new DuckDBTimeTZValue(bits, micros, offset);
    }
    static fromMicrosAndOffset(micros, offset) {
        const bits = (BigInt.asUintN(DuckDBTimeTZValue.TimeBits, micros) <<
            BigInt(DuckDBTimeTZValue.OffsetBits)) |
            BigInt.asUintN(DuckDBTimeTZValue.OffsetBits, BigInt(DuckDBTimeTZValue.MaxOffset - offset));
        return new DuckDBTimeTZValue(bits, micros, offset);
    }
    static fromParts(parts) {
        return DuckDBTimeTZValue.fromMicrosAndOffset(node_bindings_1.default.to_time(parts.time).micros, parts.offset);
    }
    static Max = DuckDBTimeTZValue.fromMicrosAndOffset(DuckDBTimeTZValue.MaxMicros, DuckDBTimeTZValue.MinOffset);
    static Min = DuckDBTimeTZValue.fromMicrosAndOffset(DuckDBTimeTZValue.MinMicros, DuckDBTimeTZValue.MaxOffset);
}
exports.DuckDBTimeTZValue = DuckDBTimeTZValue;
function timeTZValue(micros, offset) {
    return DuckDBTimeTZValue.fromMicrosAndOffset(micros, offset);
}
