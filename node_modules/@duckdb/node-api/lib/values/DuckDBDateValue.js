"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBDateValue = void 0;
exports.dateValue = dateValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const dateTimeStringConversion_1 = require("../conversion/dateTimeStringConversion");
class DuckDBDateValue {
    days;
    constructor(days) {
        this.days = days;
    }
    get isFinite() {
        return node_bindings_1.default.is_finite_date(this);
    }
    toString() {
        return (0, dateTimeStringConversion_1.getDuckDBDateStringFromDays)(this.days);
    }
    toParts() {
        return node_bindings_1.default.from_date(this);
    }
    static fromParts(parts) {
        return new DuckDBDateValue(node_bindings_1.default.to_date(parts).days);
    }
    static Epoch = new DuckDBDateValue(0);
    static Max = new DuckDBDateValue(2 ** 31 - 2);
    static Min = new DuckDBDateValue(-(2 ** 31 - 2));
    static PosInf = new DuckDBDateValue(2 ** 31 - 1);
    static NegInf = new DuckDBDateValue(-(2 ** 31 - 1));
}
exports.DuckDBDateValue = DuckDBDateValue;
function dateValue(daysOrParts) {
    if (typeof daysOrParts === 'number') {
        return new DuckDBDateValue(daysOrParts);
    }
    return DuckDBDateValue.fromParts(daysOrParts);
}
