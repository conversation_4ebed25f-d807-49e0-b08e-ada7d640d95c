import { Timestamp, TimestampParts } from '@duckdb/node-bindings';
export type { TimestampParts };
export declare class DuckDBTimestampValue implements Timestamp {
    readonly micros: bigint;
    constructor(micros: bigint);
    get isFinite(): boolean;
    toString(): string;
    toParts(): TimestampParts;
    static fromParts(parts: TimestampParts): DuckDBTimestampValue;
    static readonly Epoch: DuckDBTimestampValue;
    static readonly Max: DuckDBTimestampValue;
    static readonly Min: DuckDBTimestampValue;
    static readonly PosInf: DuckDBTimestampValue;
    static readonly NegInf: DuckDBTimestampValue;
}
export type DuckDBTimestampMicrosecondsValue = DuckDBTimestampValue;
export declare const DuckDBTimestampMicrosecondsValue: typeof DuckDBTimestampValue;
export declare function timestampValue(microsOrParts: bigint | TimestampParts): DuckDBTimestampValue;
