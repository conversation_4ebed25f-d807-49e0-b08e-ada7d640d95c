import { Timestamp, TimestampParts } from '@duckdb/node-bindings';
export declare class DuckDBTimestampTZValue implements Timestamp {
    static timezoneOffsetInMinutes: number;
    readonly micros: bigint;
    constructor(micros: bigint);
    get isFinite(): boolean;
    toString(): string;
    toParts(): TimestampParts;
    static fromParts(parts: TimestampParts): DuckDBTimestampTZValue;
    static readonly Epoch: DuckDBTimestampTZValue;
    static readonly Max: DuckDBTimestampTZValue;
    static readonly Min: DuckDBTimestampTZValue;
    static readonly PosInf: DuckDBTimestampTZValue;
    static readonly NegInf: DuckDBTimestampTZValue;
}
export declare function timestampTZValue(microsOrParts: bigint | TimestampParts): DuckDBTimestampTZValue;
