import { TimestampNanoseconds } from '@duckdb/node-bindings';
export declare class DuckDBTimestampNanosecondsValue implements TimestampNanoseconds {
    readonly nanos: bigint;
    constructor(nanos: bigint);
    get isFinite(): boolean;
    toString(): string;
    static readonly Epoch: DuckDBTimestampNanosecondsValue;
    static readonly Max: DuckDBTimestampNanosecondsValue;
    static readonly Min: DuckDBTimestampNanosecondsValue;
    static readonly PosInf: DuckDBTimestampNanosecondsValue;
    static readonly NegInf: DuckDBTimestampNanosecondsValue;
}
export declare function timestampNanosValue(nanos: bigint): DuckDBTimestampNanosecondsValue;
