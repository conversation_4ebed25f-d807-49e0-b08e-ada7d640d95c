"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBTimestampSecondsValue = void 0;
exports.timestampSecondsValue = timestampSecondsValue;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const dateTimeStringConversion_1 = require("../conversion/dateTimeStringConversion");
class DuckDBTimestampSecondsValue {
    seconds;
    constructor(seconds) {
        this.seconds = seconds;
    }
    get isFinite() {
        return node_bindings_1.default.is_finite_timestamp_s(this);
    }
    toString() {
        return (0, dateTimeStringConversion_1.getDuckDBTimestampStringFromSeconds)(this.seconds);
    }
    static Epoch = new DuckDBTimestampSecondsValue(0n);
    static Max = new DuckDBTimestampSecondsValue(9223372036854n);
    static Min = new DuckDBTimestampSecondsValue(-9223372022400n); // from test_all_types() select epoch(timestamp_s)::bigint;
    static PosInf = new DuckDBTimestampSecondsValue(2n ** 63n - 1n);
    static NegInf = new DuckDBTimestampSecondsValue(-(2n ** 63n - 1n));
}
exports.DuckDBTimestampSecondsValue = DuckDBTimestampSecondsValue;
function timestampSecondsValue(seconds) {
    return new DuckDBTimestampSecondsValue(seconds);
}
