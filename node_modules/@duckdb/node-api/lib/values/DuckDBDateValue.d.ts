import { Date_, DateParts } from '@duckdb/node-bindings';
export type { DateParts };
export declare class DuckDBDateValue implements Date_ {
    readonly days: number;
    constructor(days: number);
    get isFinite(): boolean;
    toString(): string;
    toParts(): DateParts;
    static fromParts(parts: DateParts): DuckDBDateValue;
    static readonly Epoch: DuckDBDateValue;
    static readonly Max: DuckDBDateValue;
    static readonly Min: DuckDBDateValue;
    static readonly PosInf: DuckDBDateValue;
    static readonly NegInf: DuckDBDateValue;
}
export declare function dateValue(daysOrParts: number | DateParts): DuckDBDateValue;
