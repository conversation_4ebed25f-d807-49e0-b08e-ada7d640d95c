export declare function getDuckDBDateStringFromYearMonthDay(year: number, month: number, dayOfMonth: number): string;
export declare function getDuckDBDateStringFromDays(days: number): string;
export declare function getTimezoneOffsetString(timezoneOffsetInMinutes?: number): string | undefined;
export declare function getAbsoluteOffsetStringFromParts(hoursPart: number, minutesPart: number, secondsPart: number): string;
export declare function getOffsetStringFromAbsoluteSeconds(absoluteOffsetInSeconds: number): string;
export declare function getOffsetStringFromSeconds(offsetInSeconds: number): string;
export declare function getDuckDBTimeStringFromParts(hoursPart: bigint, minutesPart: bigint, secondsPart: bigint, microsecondsPart: bigint): string;
export declare function getDuckDBTimeStringFromPartsNS(hoursPart: bigint, minutesPart: bigint, secondsPart: bigint, nanosecondsPart: bigint): string;
export declare function getDuckDBTimeStringFromPositiveMicroseconds(positiveMicroseconds: bigint): string;
export declare function getDuckDBTimeStringFromPositiveNanoseconds(positiveNanoseconds: bigint): string;
export declare function getDuckDBTimeStringFromMicrosecondsInDay(microsecondsInDay: bigint): string;
export declare function getDuckDBTimeStringFromNanosecondsInDay(nanosecondsInDay: bigint): string;
export declare function getDuckDBTimeStringFromMicroseconds(microseconds: bigint): string;
export declare function getDuckDBTimestampStringFromDaysAndMicroseconds(days: bigint, microsecondsInDay: bigint, timezonePart?: string): string;
export declare function getDuckDBTimestampStringFromDaysAndNanoseconds(days: bigint, nanosecondsInDay: bigint): string;
export declare function getDuckDBTimestampStringFromMicroseconds(microseconds: bigint, timezoneOffsetInMinutes?: number): string;
export declare function getDuckDBTimestampStringFromSeconds(seconds: bigint): string;
export declare function getDuckDBTimestampStringFromMilliseconds(milliseconds: bigint): string;
export declare function getDuckDBTimestampStringFromNanoseconds(nanoseconds: bigint): string;
export declare function getDuckDBIntervalString(months: number, days: number, microseconds: bigint): string;
