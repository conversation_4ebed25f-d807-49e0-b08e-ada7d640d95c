"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBBigNumVector = exports.DuckDBTimestampTZVector = exports.DuckDBTimeTZVector = exports.DuckDBBitVector = exports.DuckDBUnionVector = exports.DuckDBUUIDVector = exports.DuckDBArrayVector = exports.DuckDBMapVector = exports.DuckDBStructVector = exports.DuckDBListVector = exports.DuckDBEnum32Vector = exports.DuckDBEnum16Vector = exports.DuckDBEnum8Vector = exports.DuckDBTimestampNanosecondsVector = exports.DuckDBTimestampMillisecondsVector = exports.DuckDBTimestampSecondsVector = exports.DuckDBDecimal128Vector = exports.DuckDBDecimal64Vector = exports.DuckDBDecimal32Vector = exports.DuckDBDecimal16Vector = exports.DuckDBBlobVector = exports.DuckDBVarCharVector = exports.DuckDBUHugeIntVector = exports.DuckDBHugeIntVector = exports.DuckDBIntervalVector = exports.DuckDBTimeVector = exports.DuckDBDateVector = exports.DuckDBTimestampVector = exports.DuckDBDoubleVector = exports.DuckDBFloatVector = exports.DuckDBUBigIntVector = exports.DuckDBUIntegerVector = exports.DuckDBUSmallIntVector = exports.DuckDBUTinyIntVector = exports.DuckDBBigIntVector = exports.DuckDBIntegerVector = exports.DuckDBSmallIntVector = exports.DuckDBTinyIntVector = exports.DuckDBBooleanVector = exports.DuckDBVector = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const os_1 = __importDefault(require("os"));
const DuckDBLogicalType_1 = require("./DuckDBLogicalType");
const DuckDBType_1 = require("./DuckDBType");
const DuckDBTypeId_1 = require("./DuckDBTypeId");
const values_1 = require("./values");
const littleEndian = os_1.default.endianness() === 'LE';
// function getInt8(dataView: DataView, offset: number): number {
//   return dataView.getInt8(offset);
// }
function getUInt8(dataView, offset) {
    return dataView.getUint8(offset);
}
function getInt16(dataView, offset) {
    return dataView.getInt16(offset, littleEndian);
}
function getUInt16(dataView, offset) {
    return dataView.getUint16(offset, littleEndian);
}
function getInt32(dataView, offset) {
    return dataView.getInt32(offset, littleEndian);
}
function getUInt32(dataView, offset) {
    return dataView.getUint32(offset, littleEndian);
}
function getInt64(dataView, offset) {
    return dataView.getBigInt64(offset, littleEndian);
}
function getUInt64(dataView, offset) {
    return dataView.getBigUint64(offset, littleEndian);
}
// function getFloat32(dataView: DataView, offset: number): number {
//   return dataView.getFloat32(offset, littleEndian);
// }
// function getFloat64(dataView: DataView, offset: number): number {
//   return dataView.getFloat64(offset, littleEndian);
// }
function getInt128(dataView, offset) {
    const lower = getUInt64(dataView, offset);
    const upper = getInt64(dataView, offset + 8);
    return (upper << BigInt(64)) + lower;
}
function getUInt128(dataView, offset) {
    const lower = getUInt64(dataView, offset);
    const upper = getUInt64(dataView, offset + 8);
    return (BigInt.asUintN(64, upper) << BigInt(64)) | BigInt.asUintN(64, lower);
}
function getStringBytes(dataView, offset) {
    const lengthInBytes = dataView.getUint32(offset, true);
    if (lengthInBytes <= 12) {
        return new Uint8Array(dataView.buffer, dataView.byteOffset + offset + 4, lengthInBytes);
    }
    else {
        return node_bindings_1.default.get_data_from_pointer(dataView.buffer, dataView.byteOffset + offset + 8, lengthInBytes);
    }
}
const textDecoder = new TextDecoder();
function getString(dataView, offset) {
    const stringBytes = getStringBytes(dataView, offset);
    return textDecoder.decode(stringBytes);
}
function getBuffer(dataView, offset) {
    const stringBytes = getStringBytes(dataView, offset);
    return Buffer.from(stringBytes);
}
function getBigNumFromBytes(bytes) {
    const firstByte = bytes[0];
    const positive = (firstByte & 0x80) > 0;
    const uint64Mask = positive ? 0n : 0xffffffffffffffffn;
    const uint8Mask = positive ? 0 : 0xff;
    const dv = new DataView(// bytes is big endian
    bytes.buffer, bytes.byteOffset + 3, bytes.byteLength - 3);
    const lastUint64Offset = dv.byteLength - 8;
    let offset = 0;
    let result = 0n;
    while (offset <= lastUint64Offset) {
        result = (result << 64n) | (dv.getBigUint64(offset) ^ uint64Mask);
        offset += 8;
    }
    while (offset < dv.byteLength) {
        result = (result << 8n) | BigInt(dv.getUint8(offset) ^ uint8Mask);
        offset += 1;
    }
    return positive ? result : -result;
}
function getBytesFromBigNum(bignum) {
    const numberBytes = []; // little endian
    const negative = bignum < 0;
    if (bignum === 0n) {
        numberBytes.push(0);
    }
    else {
        let vi = bignum < 0 ? -bignum : bignum;
        while (vi !== 0n) {
            numberBytes.push(Number(BigInt.asUintN(8, vi)));
            vi >>= 8n;
        }
    }
    const bigNumBytes = new Uint8Array(3 + numberBytes.length); // big endian
    let header = 0x800000 | numberBytes.length;
    if (negative) {
        header = ~header;
    }
    bigNumBytes[0] = 0xff & (header >> 16);
    bigNumBytes[1] = 0xff & (header >> 8);
    bigNumBytes[2] = 0xff & header;
    for (let i = 0; i < numberBytes.length; i++) {
        const byte = numberBytes[numberBytes.length - 1 - i];
        bigNumBytes[3 + i] = negative ? ~byte : byte;
    }
    return bigNumBytes;
}
function getBoolean1(dataView, offset) {
    return getUInt8(dataView, offset) !== 0;
}
function getBoolean2(dataView, offset) {
    return getUInt16(dataView, offset) !== 0;
}
function getBoolean4(dataView, offset) {
    return getUInt32(dataView, offset) !== 0;
}
function getBoolean8(dataView, offset) {
    return getUInt64(dataView, offset) !== BigInt(0);
}
function makeGetBoolean() {
    switch (node_bindings_1.default.sizeof_bool) {
        case 1:
            return getBoolean1;
        case 2:
            return getBoolean2;
        case 4:
            return getBoolean4;
        case 8:
            return getBoolean8;
        default:
            throw new Error(`Unsupported boolean size: ${node_bindings_1.default.sizeof_bool}`);
    }
}
const getBoolean = makeGetBoolean();
// function setInt8(dataView: DataView, offset: number, value: number) {
//   dataView.setInt8(offset, value);
// }
function setUInt8(dataView, offset, value) {
    dataView.setUint8(offset, value);
}
function setInt16(dataView, offset, value) {
    dataView.setInt16(offset, value, littleEndian);
}
function setUInt16(dataView, offset, value) {
    dataView.setUint16(offset, value, littleEndian);
}
function setInt32(dataView, offset, value) {
    dataView.setInt32(offset, value, littleEndian);
}
function setUInt32(dataView, offset, value) {
    dataView.setUint32(offset, value, littleEndian);
}
function setInt64(dataView, offset, value) {
    dataView.setBigInt64(offset, value, littleEndian);
}
function setUInt64(dataView, offset, value) {
    dataView.setBigUint64(offset, value, littleEndian);
}
function setInt128(dataView, offset, value) {
    const lower = BigInt.asUintN(64, value);
    const upper = BigInt.asIntN(64, value >> BigInt(64));
    dataView.setBigUint64(offset, lower, littleEndian);
    dataView.setBigInt64(offset + 8, upper, littleEndian);
}
function setUInt128(dataView, offset, value) {
    const lower = BigInt.asUintN(64, value);
    const upper = BigInt.asUintN(64, value >> BigInt(64));
    dataView.setBigUint64(offset, lower, littleEndian);
    dataView.setBigUint64(offset + 8, upper, littleEndian);
}
function setBoolean1(dataView, offset, value) {
    setUInt8(dataView, offset, value ? 1 : 0);
}
function setBoolean2(dataView, offset, value) {
    setUInt16(dataView, offset, value ? 1 : 0);
}
function setBoolean4(dataView, offset, value) {
    setUInt32(dataView, offset, value ? 1 : 0);
}
function setBoolean8(dataView, offset, value) {
    setUInt64(dataView, offset, value ? BigInt(1) : BigInt(0));
}
function makeSetBoolean() {
    switch (node_bindings_1.default.sizeof_bool) {
        case 1:
            return setBoolean1;
        case 2:
            return setBoolean2;
        case 4:
            return setBoolean4;
        case 8:
            return setBoolean8;
        default:
            throw new Error(`Unsupported boolean size: ${node_bindings_1.default.sizeof_bool}`);
    }
}
const setBoolean = makeSetBoolean();
function getDecimal16(dataView, offset, type) {
    const value = getInt16(dataView, offset);
    return new values_1.DuckDBDecimalValue(BigInt(value), type.width, type.scale);
}
function getDecimal32(dataView, offset, type) {
    const value = getInt32(dataView, offset);
    return new values_1.DuckDBDecimalValue(BigInt(value), type.width, type.scale);
}
function getDecimal64(dataView, offset, type) {
    const value = getInt64(dataView, offset);
    return new values_1.DuckDBDecimalValue(value, type.width, type.scale);
}
function getDecimal128(dataView, offset, type) {
    const value = getInt128(dataView, offset);
    return new values_1.DuckDBDecimalValue(value, type.width, type.scale);
}
function vectorData(vector, byteCount) {
    return node_bindings_1.default.vector_get_data(vector, byteCount);
}
// This version of DuckDBValidity is almost 10x slower.
// class DuckDBValidity {
//   private readonly validity_pointer: ddb.uint64_pointer;
//   private readonly offset: number;
//   private constructor(validity_pointer: ddb.uint64_pointer, offset: number = 0) {
//     this.validity_pointer = validity_pointer;
//     this.offset = offset;
//   }
//   public static fromVector(vector: ddb.duckdb_vector, itemCount: number, offset: number = 0): DuckDBValidity {
//     const validity_pointer = ddb.duckdb_vector_get_validity(vector);
//     return new DuckDBValidity(validity_pointer, offset);
//   }
//   public itemValid(itemIndex: number): boolean {
//     return ddb.duckdb_validity_row_is_valid(this.validity_pointer, this.offset + itemIndex);
//   }
//   public slice(offset: number): DuckDBValidity {
//     return new DuckDBValidity(this.validity_pointer, this.offset + offset);
//   }
// }
class DuckDBValidity {
    data;
    offset;
    itemCount;
    constructor(data, offset, itemCount) {
        this.data = data;
        this.offset = offset;
        this.itemCount = itemCount;
    }
    static fromVector(vector, itemCount) {
        const uint64Count = Math.ceil(itemCount / 64);
        const bytes = node_bindings_1.default.vector_get_validity(vector, uint64Count * 8);
        if (!bytes) {
            return new DuckDBValidity(null, 0, itemCount);
        }
        const bigints = new BigUint64Array(bytes.buffer, bytes.byteOffset, uint64Count);
        return new DuckDBValidity(bigints, 0, itemCount);
    }
    itemValid(itemIndex) {
        if (!this.data) {
            return true;
        }
        const bit = this.offset + itemIndex;
        return ((this.data[Math.floor(bit / 64)] & (BigInt(1) << BigInt(bit % 64))) !==
            BigInt(0));
    }
    setItemValid(itemIndex, valid) {
        if (!this.data && !valid) {
            // An item is being set to invalid and we don't have a data buffer, so create it. If an item is being set to valid
            // and we don't have a data buffer, then there's nothing to do; a null data buffer indicates all items are valid.
            const uint64Count = Math.ceil(this.itemCount / 64);
            const buffer = new ArrayBuffer(uint64Count * 8);
            this.data = new BigUint64Array(buffer, 0, uint64Count);
            // Initialize to all items to valid.
            for (let i = 0; i < this.data.length; i++) {
                this.data[i] = 0xffffffffffffffffn;
            }
        }
        if (this.data) {
            const bit = this.offset + itemIndex;
            const uint64Index = Math.floor(bit / 64);
            const uint64WithBitSet = BigInt(1) << BigInt(bit % 64);
            if (valid) {
                if ((this.data[uint64Index] & uint64WithBitSet) === 0n) {
                    this.data[uint64Index] |= uint64WithBitSet;
                }
            }
            else {
                if ((this.data[uint64Index] & uint64WithBitSet) !== 0n) {
                    this.data[uint64Index] &= ~uint64WithBitSet;
                }
            }
        }
    }
    flush(vector) {
        if (this.data) {
            node_bindings_1.default.vector_ensure_validity_writable(vector);
            node_bindings_1.default.copy_data_to_vector_validity(vector, 0, this.data.buffer, this.data.byteOffset, this.data.byteLength);
        }
    }
    slice(offset, itemCount) {
        return new DuckDBValidity(this.data, this.offset + offset, itemCount);
    }
}
class DuckDBVector {
    static standardSize() {
        return node_bindings_1.default.vector_size();
    }
    static create(vector, itemCount, knownType) {
        const vectorType = knownType
            ? knownType
            : DuckDBLogicalType_1.DuckDBLogicalType.create(node_bindings_1.default.vector_get_column_type(vector)).asType();
        switch (vectorType.typeId) {
            case DuckDBTypeId_1.DuckDBTypeId.BOOLEAN:
                return DuckDBBooleanVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.TINYINT:
                return DuckDBTinyIntVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.SMALLINT:
                return DuckDBSmallIntVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.INTEGER:
                return DuckDBIntegerVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.BIGINT:
                return DuckDBBigIntVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.UTINYINT:
                return DuckDBUTinyIntVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.USMALLINT:
                return DuckDBUSmallIntVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.UINTEGER:
                return DuckDBUIntegerVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.UBIGINT:
                return DuckDBUBigIntVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.FLOAT:
                return DuckDBFloatVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.DOUBLE:
                return DuckDBDoubleVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP:
                return DuckDBTimestampVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.DATE:
                return DuckDBDateVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.TIME:
                return DuckDBTimeVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.INTERVAL:
                return DuckDBIntervalVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.HUGEINT:
                return DuckDBHugeIntVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.UHUGEINT:
                return DuckDBUHugeIntVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.VARCHAR:
                return DuckDBVarCharVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.BLOB:
                return DuckDBBlobVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.DECIMAL:
                if (vectorType instanceof DuckDBType_1.DuckDBDecimalType) {
                    const { width } = vectorType;
                    if (width <= 0) {
                        throw new Error(`DECIMAL width not positive: ${width}`);
                    }
                    else if (width <= 4) {
                        return DuckDBDecimal16Vector.fromRawVector(vectorType, vector, itemCount);
                    }
                    else if (width <= 9) {
                        return DuckDBDecimal32Vector.fromRawVector(vectorType, vector, itemCount);
                    }
                    else if (width <= 18) {
                        return DuckDBDecimal64Vector.fromRawVector(vectorType, vector, itemCount);
                    }
                    else if (width <= 38) {
                        return DuckDBDecimal128Vector.fromRawVector(vectorType, vector, itemCount);
                    }
                    else {
                        throw new Error(`DECIMAL width too large: ${width}`);
                    }
                }
                throw new Error('DuckDBType has DECIMAL type id but is not an instance of DuckDBDecimalType');
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_S:
                return DuckDBTimestampSecondsVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_MS:
                return DuckDBTimestampMillisecondsVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_NS:
                return DuckDBTimestampNanosecondsVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.ENUM:
                if (vectorType instanceof DuckDBType_1.DuckDBEnumType) {
                    const { internalTypeId } = vectorType;
                    switch (internalTypeId) {
                        case DuckDBTypeId_1.DuckDBTypeId.UTINYINT:
                            return DuckDBEnum8Vector.fromRawVector(vectorType, vector, itemCount);
                        case DuckDBTypeId_1.DuckDBTypeId.USMALLINT:
                            return DuckDBEnum16Vector.fromRawVector(vectorType, vector, itemCount);
                        case DuckDBTypeId_1.DuckDBTypeId.UINTEGER:
                            return DuckDBEnum32Vector.fromRawVector(vectorType, vector, itemCount);
                        default:
                            throw new Error(`unsupported ENUM internal type: ${internalTypeId}`);
                    }
                }
                throw new Error('DuckDBType has ENUM type id but is not an instance of DuckDBEnumType');
            case DuckDBTypeId_1.DuckDBTypeId.LIST:
                if (vectorType instanceof DuckDBType_1.DuckDBListType) {
                    return DuckDBListVector.fromRawVector(vectorType, vector, itemCount);
                }
                throw new Error('DuckDBType has LIST type id but is not an instance of DuckDBListType');
            case DuckDBTypeId_1.DuckDBTypeId.STRUCT:
                if (vectorType instanceof DuckDBType_1.DuckDBStructType) {
                    return DuckDBStructVector.fromRawVector(vectorType, vector, itemCount);
                }
                throw new Error('DuckDBType has STRUCT type id but is not an instance of DuckDBStructType');
            case DuckDBTypeId_1.DuckDBTypeId.MAP:
                if (vectorType instanceof DuckDBType_1.DuckDBMapType) {
                    return DuckDBMapVector.fromRawVector(vectorType, vector, itemCount);
                }
                throw new Error('DuckDBType has MAP type id but is not an instance of DuckDBMapType');
            case DuckDBTypeId_1.DuckDBTypeId.ARRAY:
                if (vectorType instanceof DuckDBType_1.DuckDBArrayType) {
                    return DuckDBArrayVector.fromRawVector(vectorType, vector, itemCount);
                }
                throw new Error('DuckDBType has ARRAY type id but is not an instance of DuckDBArrayType');
            case DuckDBTypeId_1.DuckDBTypeId.UUID:
                return DuckDBUUIDVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.UNION:
                if (vectorType instanceof DuckDBType_1.DuckDBUnionType) {
                    return DuckDBUnionVector.fromRawVector(vectorType, vector, itemCount);
                }
                throw new Error('DuckDBType has UNION type id but is not an instance of DuckDBUnionType');
            case DuckDBTypeId_1.DuckDBTypeId.BIT:
                return DuckDBBitVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.TIME_TZ:
                return DuckDBTimeTZVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_TZ:
                return DuckDBTimestampTZVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.ANY:
                throw new Error(`Invalid vector type: ANY`);
            case DuckDBTypeId_1.DuckDBTypeId.BIGNUM:
                return DuckDBBigNumVector.fromRawVector(vector, itemCount);
            case DuckDBTypeId_1.DuckDBTypeId.SQLNULL:
                throw new Error(`Invalid vector type: SQLNULL`);
            default:
                throw new Error(`Invalid type id: ${vectorType.typeId}`);
        }
    }
    toArray() {
        const items = [];
        for (let i = 0; i < this.itemCount; i++) {
            items.push(this.getItem(i));
        }
        return items;
    }
}
exports.DuckDBVector = DuckDBVector;
class DuckDBBooleanVector extends DuckDBVector {
    dataView;
    validity;
    vector;
    _itemCount;
    constructor(dataView, validity, vector, itemCount) {
        super();
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this._itemCount = itemCount;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * node_bindings_1.default.sizeof_bool);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBBooleanVector(dataView, validity, vector, itemCount);
    }
    get type() {
        return DuckDBType_1.DuckDBBooleanType.instance;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getBoolean(this.dataView, itemIndex * node_bindings_1.default.sizeof_bool)
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            setBoolean(this.dataView, itemIndex * node_bindings_1.default.sizeof_bool, value);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.dataView.buffer, this.dataView.byteOffset, this.dataView.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBBooleanVector(new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * node_bindings_1.default.sizeof_bool, length * node_bindings_1.default.sizeof_bool), this.validity.slice(offset, length), this.vector, length);
    }
}
exports.DuckDBBooleanVector = DuckDBBooleanVector;
class DuckDBTinyIntVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * Int8Array.BYTES_PER_ELEMENT);
        const items = new Int8Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBTinyIntVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBTinyIntType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBTinyIntVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBTinyIntVector = DuckDBTinyIntVector;
class DuckDBSmallIntVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * Int16Array.BYTES_PER_ELEMENT);
        const items = new Int16Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBSmallIntVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBSmallIntType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBSmallIntVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBSmallIntVector = DuckDBSmallIntVector;
class DuckDBIntegerVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * Int32Array.BYTES_PER_ELEMENT);
        const items = new Int32Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBIntegerVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBIntegerType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBIntegerVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBIntegerVector = DuckDBIntegerVector;
class DuckDBBigIntVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * BigInt64Array.BYTES_PER_ELEMENT);
        const items = new BigInt64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBBigIntVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBBigIntType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBBigIntVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBBigIntVector = DuckDBBigIntVector;
class DuckDBUTinyIntVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * Uint8Array.BYTES_PER_ELEMENT);
        const items = new Uint8Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBUTinyIntVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBUTinyIntType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBUTinyIntVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBUTinyIntVector = DuckDBUTinyIntVector;
class DuckDBUSmallIntVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * Uint16Array.BYTES_PER_ELEMENT);
        const items = new Uint16Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBUSmallIntVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBUSmallIntType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBUSmallIntVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBUSmallIntVector = DuckDBUSmallIntVector;
class DuckDBUIntegerVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * Uint32Array.BYTES_PER_ELEMENT);
        const items = new Uint32Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBUIntegerVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBUIntegerType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBUIntegerVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBUIntegerVector = DuckDBUIntegerVector;
class DuckDBUBigIntVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * BigUint64Array.BYTES_PER_ELEMENT);
        const items = new BigUint64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBUBigIntVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBUBigIntType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBUBigIntVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBUBigIntVector = DuckDBUBigIntVector;
class DuckDBFloatVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * Float32Array.BYTES_PER_ELEMENT);
        const items = new Float32Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBFloatVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBFloatType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBFloatVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBFloatVector = DuckDBFloatVector;
class DuckDBDoubleVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * Float64Array.BYTES_PER_ELEMENT);
        const items = new Float64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBDoubleVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBDoubleType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex) ? this.items[itemIndex] : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBDoubleVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBDoubleVector = DuckDBDoubleVector;
class DuckDBTimestampVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * BigInt64Array.BYTES_PER_ELEMENT);
        const items = new BigInt64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBTimestampVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBTimestampType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? new values_1.DuckDBTimestampValue(this.items[itemIndex])
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value.micros;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBTimestampVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBTimestampVector = DuckDBTimestampVector;
class DuckDBDateVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * Int32Array.BYTES_PER_ELEMENT);
        const items = new Int32Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBDateVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBDateType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? new values_1.DuckDBDateValue(this.items[itemIndex])
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value.days;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBDateVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBDateVector = DuckDBDateVector;
class DuckDBTimeVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * BigInt64Array.BYTES_PER_ELEMENT);
        const items = new BigInt64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBTimeVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBTimeType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? new values_1.DuckDBTimeValue(this.items[itemIndex])
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value.micros;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBTimeVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBTimeVector = DuckDBTimeVector;
class DuckDBIntervalVector extends DuckDBVector {
    dataView;
    validity;
    vector;
    _itemCount;
    constructor(dataView, validity, vector, itemCount) {
        super();
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this._itemCount = itemCount;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * 16);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBIntervalVector(dataView, validity, vector, itemCount);
    }
    get type() {
        return DuckDBType_1.DuckDBIntervalType.instance;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        if (!this.validity.itemValid(itemIndex)) {
            return null;
        }
        const itemStart = itemIndex * 16;
        const months = getInt32(this.dataView, itemStart);
        const days = getInt32(this.dataView, itemStart + 4);
        const micros = getInt64(this.dataView, itemStart + 8);
        return new values_1.DuckDBIntervalValue(months, days, micros);
    }
    setItem(itemIndex, value) {
        if (value != null) {
            const itemStart = itemIndex * 16;
            setInt32(this.dataView, itemStart, value.months);
            setInt32(this.dataView, itemStart + 4, value.days);
            setInt64(this.dataView, itemStart + 8, value.micros);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.dataView.buffer, this.dataView.byteOffset, this.dataView.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBIntervalVector(new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 16, length * 16), this.validity.slice(offset, length), this.vector, length);
    }
}
exports.DuckDBIntervalVector = DuckDBIntervalVector;
class DuckDBHugeIntVector extends DuckDBVector {
    dataView;
    validity;
    vector;
    _itemCount;
    constructor(dataView, validity, vector, itemCount) {
        super();
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this._itemCount = itemCount;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * 16);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBHugeIntVector(dataView, validity, vector, itemCount);
    }
    get type() {
        return DuckDBType_1.DuckDBHugeIntType.instance;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getInt128(this.dataView, itemIndex * 16)
            : null;
    }
    getDouble(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? node_bindings_1.default.hugeint_to_double(getInt128(this.dataView, itemIndex * 16))
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            setInt128(this.dataView, itemIndex * 16, value);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.dataView.buffer, this.dataView.byteOffset, this.dataView.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBHugeIntVector(new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 16, length * 16), this.validity.slice(offset, length), this.vector, length);
    }
}
exports.DuckDBHugeIntVector = DuckDBHugeIntVector;
class DuckDBUHugeIntVector extends DuckDBVector {
    dataView;
    validity;
    vector;
    _itemCount;
    constructor(dataView, validity, vector, itemCount) {
        super();
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this._itemCount = itemCount;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * 16);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBUHugeIntVector(dataView, validity, vector, itemCount);
    }
    get type() {
        return DuckDBType_1.DuckDBUHugeIntType.instance;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getUInt128(this.dataView, itemIndex * 16)
            : null;
    }
    getDouble(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? node_bindings_1.default.uhugeint_to_double(getUInt128(this.dataView, itemIndex * 16))
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            setUInt128(this.dataView, itemIndex * 16, value);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.dataView.buffer, this.dataView.byteOffset, this.dataView.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBUHugeIntVector(new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 16, length * 16), this.validity.slice(offset, length), this.vector, length);
    }
}
exports.DuckDBUHugeIntVector = DuckDBUHugeIntVector;
class DuckDBVarCharVector extends DuckDBVector {
    dataView;
    validity;
    vector;
    itemOffset;
    _itemCount;
    itemCache;
    itemCacheDirty;
    constructor(dataView, validity, vector, itemOffset, itemCount) {
        super();
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this.itemOffset = itemOffset;
        this._itemCount = itemCount;
        this.itemCache = [];
        this.itemCacheDirty = [];
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * 16);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBVarCharVector(dataView, validity, vector, 0, itemCount);
    }
    get type() {
        return DuckDBType_1.DuckDBVarCharType.instance;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        const cachedItem = this.itemCache[itemIndex];
        if (cachedItem !== undefined) {
            return cachedItem;
        }
        const item = this.validity.itemValid(itemIndex)
            ? getString(this.dataView, itemIndex * 16)
            : null;
        this.itemCache[itemIndex] = item;
        return item;
    }
    setItem(itemIndex, value) {
        this.itemCache[itemIndex] = value;
        this.validity.setItemValid(itemIndex, value != null);
        this.itemCacheDirty[itemIndex] = true;
    }
    flush() {
        for (let itemIndex = 0; itemIndex < this._itemCount; itemIndex++) {
            if (this.itemCacheDirty[itemIndex]) {
                const cachedItem = this.itemCache[itemIndex];
                if (cachedItem !== undefined && cachedItem !== null) {
                    node_bindings_1.default.vector_assign_string_element(this.vector, this.itemOffset + itemIndex, cachedItem);
                }
                this.itemCacheDirty[itemIndex] = false;
            }
        }
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBVarCharVector(new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 16, length * 16), this.validity.slice(offset, length), this.vector, offset, length);
    }
}
exports.DuckDBVarCharVector = DuckDBVarCharVector;
class DuckDBBlobVector extends DuckDBVector {
    dataView;
    validity;
    vector;
    itemOffset;
    _itemCount;
    itemCache;
    itemCacheDirty;
    constructor(dataView, validity, vector, itemOffset, itemCount) {
        super();
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this.itemOffset = itemOffset;
        this._itemCount = itemCount;
        this.itemCache = [];
        this.itemCacheDirty = [];
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * 16);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBBlobVector(dataView, validity, vector, 0, itemCount);
    }
    get type() {
        return DuckDBType_1.DuckDBBlobType.instance;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? new values_1.DuckDBBlobValue(getBuffer(this.dataView, itemIndex * 16))
            : null;
    }
    setItem(itemIndex, value) {
        this.itemCache[itemIndex] = value;
        this.validity.setItemValid(itemIndex, value != null);
        this.itemCacheDirty[itemIndex] = true;
    }
    flush() {
        for (let itemIndex = 0; itemIndex < this._itemCount; itemIndex++) {
            if (this.itemCacheDirty[itemIndex]) {
                const cachedItem = this.itemCache[itemIndex];
                if (cachedItem !== undefined && cachedItem !== null) {
                    node_bindings_1.default.vector_assign_string_element_len(this.vector, this.itemOffset + itemIndex, cachedItem.bytes);
                }
                this.itemCacheDirty[itemIndex] = false;
            }
        }
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBBlobVector(new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 16, length * 16), this.validity.slice(offset, length), this.vector, offset, length);
    }
}
exports.DuckDBBlobVector = DuckDBBlobVector;
class DuckDBDecimal16Vector extends DuckDBVector {
    decimalType;
    dataView;
    validity;
    vector;
    _itemCount;
    constructor(decimalType, dataView, validity, vector, itemCount) {
        super();
        this.decimalType = decimalType;
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this._itemCount = itemCount;
    }
    static fromRawVector(decimalType, vector, itemCount) {
        const data = vectorData(vector, itemCount * 2);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBDecimal16Vector(decimalType, dataView, validity, vector, itemCount);
    }
    get type() {
        return this.decimalType;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getDecimal16(this.dataView, itemIndex * 2, this.decimalType)
            : null;
    }
    getScaledValue(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getInt16(this.dataView, itemIndex * 2)
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            setInt16(this.dataView, itemIndex * 2, Number(value.value));
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.dataView.buffer, this.dataView.byteOffset, this.dataView.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBDecimal16Vector(this.decimalType, new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 2, length * 2), this.validity.slice(offset, length), this.vector, length);
    }
}
exports.DuckDBDecimal16Vector = DuckDBDecimal16Vector;
class DuckDBDecimal32Vector extends DuckDBVector {
    decimalType;
    dataView;
    validity;
    vector;
    _itemCount;
    constructor(decimalType, dataView, validity, vector, itemCount) {
        super();
        this.decimalType = decimalType;
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this._itemCount = itemCount;
    }
    static fromRawVector(decimalType, vector, itemCount) {
        const data = vectorData(vector, itemCount * 4);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBDecimal32Vector(decimalType, dataView, validity, vector, itemCount);
    }
    get type() {
        return this.decimalType;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getDecimal32(this.dataView, itemIndex * 4, this.decimalType)
            : null;
    }
    getScaledValue(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getInt32(this.dataView, itemIndex * 4)
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            setInt32(this.dataView, itemIndex * 4, Number(value.value));
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.dataView.buffer, this.dataView.byteOffset, this.dataView.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBDecimal32Vector(this.decimalType, new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 4, length * 4), this.validity.slice(offset, length), this.vector, length);
    }
}
exports.DuckDBDecimal32Vector = DuckDBDecimal32Vector;
class DuckDBDecimal64Vector extends DuckDBVector {
    decimalType;
    dataView;
    validity;
    vector;
    _itemCount;
    constructor(decimalType, dataView, validity, vector, itemCount) {
        super();
        this.decimalType = decimalType;
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this._itemCount = itemCount;
    }
    static fromRawVector(decimalType, vector, itemCount) {
        const data = vectorData(vector, itemCount * 8);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBDecimal64Vector(decimalType, dataView, validity, vector, itemCount);
    }
    get type() {
        return this.decimalType;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getDecimal64(this.dataView, itemIndex * 8, this.decimalType)
            : null;
    }
    getScaledValue(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getInt64(this.dataView, itemIndex * 8)
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            setInt64(this.dataView, itemIndex * 8, value.value);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.dataView.buffer, this.dataView.byteOffset, this.dataView.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBDecimal64Vector(this.decimalType, new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 8, length * 8), this.validity.slice(offset, length), this.vector, length);
    }
}
exports.DuckDBDecimal64Vector = DuckDBDecimal64Vector;
class DuckDBDecimal128Vector extends DuckDBVector {
    decimalType;
    dataView;
    validity;
    vector;
    _itemCount;
    constructor(decimalType, dataView, validity, vector, itemCount) {
        super();
        this.decimalType = decimalType;
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this._itemCount = itemCount;
    }
    static fromRawVector(decimalType, vector, itemCount) {
        const data = vectorData(vector, itemCount * 16);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBDecimal128Vector(decimalType, dataView, validity, vector, itemCount);
    }
    get type() {
        return this.decimalType;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getDecimal128(this.dataView, itemIndex * 16, this.decimalType)
            : null;
    }
    getScaledValue(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? getInt128(this.dataView, itemIndex * 16)
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            setInt128(this.dataView, itemIndex * 16, value.value);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.dataView.buffer, this.dataView.byteOffset, this.dataView.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBDecimal128Vector(this.decimalType, new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 16, length * 16), this.validity.slice(offset, length), this.vector, length);
    }
}
exports.DuckDBDecimal128Vector = DuckDBDecimal128Vector;
class DuckDBTimestampSecondsVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * BigInt64Array.BYTES_PER_ELEMENT);
        const items = new BigInt64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBTimestampSecondsVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBTimestampSecondsType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? new values_1.DuckDBTimestampSecondsValue(this.items[itemIndex])
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value.seconds;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBTimestampSecondsVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBTimestampSecondsVector = DuckDBTimestampSecondsVector;
class DuckDBTimestampMillisecondsVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * BigInt64Array.BYTES_PER_ELEMENT);
        const items = new BigInt64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBTimestampMillisecondsVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBTimestampMillisecondsType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? new values_1.DuckDBTimestampMillisecondsValue(this.items[itemIndex])
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value.millis;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBTimestampMillisecondsVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBTimestampMillisecondsVector = DuckDBTimestampMillisecondsVector;
class DuckDBTimestampNanosecondsVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * BigInt64Array.BYTES_PER_ELEMENT);
        const items = new BigInt64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBTimestampNanosecondsVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBTimestampNanosecondsType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? new values_1.DuckDBTimestampNanosecondsValue(this.items[itemIndex])
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value.nanos;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBTimestampNanosecondsVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBTimestampNanosecondsVector = DuckDBTimestampNanosecondsVector;
class DuckDBEnum8Vector extends DuckDBVector {
    enumType;
    items;
    validity;
    vector;
    constructor(enumType, items, validity, vector) {
        super();
        this.enumType = enumType;
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(enumType, vector, itemCount) {
        const data = vectorData(vector, itemCount);
        const items = new Uint8Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBEnum8Vector(enumType, items, validity, vector);
    }
    get type() {
        return this.enumType;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? this.enumType.values[this.items[itemIndex]]
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = this.enumType.indexForValue(value);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBEnum8Vector(this.enumType, this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBEnum8Vector = DuckDBEnum8Vector;
class DuckDBEnum16Vector extends DuckDBVector {
    enumType;
    items;
    validity;
    vector;
    constructor(enumType, items, validity, vector) {
        super();
        this.enumType = enumType;
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(enumType, vector, itemCount) {
        const data = vectorData(vector, itemCount * 2);
        const items = new Uint16Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBEnum16Vector(enumType, items, validity, vector);
    }
    get type() {
        return this.enumType;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? this.enumType.values[this.items[itemIndex]]
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = this.enumType.indexForValue(value);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBEnum16Vector(this.enumType, this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBEnum16Vector = DuckDBEnum16Vector;
class DuckDBEnum32Vector extends DuckDBVector {
    enumType;
    items;
    validity;
    vector;
    constructor(enumType, items, validity, vector) {
        super();
        this.enumType = enumType;
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(enumType, vector, itemCount) {
        const data = vectorData(vector, itemCount * 4);
        const items = new Uint32Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBEnum32Vector(enumType, items, validity, vector);
    }
    get type() {
        return this.enumType;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? this.enumType.values[this.items[itemIndex]]
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = this.enumType.indexForValue(value);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBEnum32Vector(this.enumType, this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBEnum32Vector = DuckDBEnum32Vector;
class DuckDBListVector extends DuckDBVector {
    parentList;
    listType;
    entryData;
    validity;
    vector;
    childData;
    itemOffset;
    _itemCount;
    itemCache;
    constructor(parentList, listType, entryData, validity, vector, childData, itemOffset, itemCount) {
        super();
        this.parentList = parentList;
        this.listType = listType;
        this.entryData = entryData;
        this.validity = validity;
        this.vector = vector;
        this.childData = childData;
        this.itemOffset = itemOffset,
            this._itemCount = itemCount;
        this.itemCache = [];
    }
    static fromRawVector(listType, vector, itemCount) {
        const data = vectorData(vector, itemCount * BigUint64Array.BYTES_PER_ELEMENT * 2);
        const entryData = new BigUint64Array(data.buffer, data.byteOffset, itemCount * 2);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        const child_vector = node_bindings_1.default.list_vector_get_child(vector);
        const child_vector_size = node_bindings_1.default.list_vector_get_size(vector);
        const childData = DuckDBVector.create(child_vector, child_vector_size, listType.valueType);
        return new DuckDBListVector(null, listType, entryData, validity, vector, childData, 0, itemCount);
    }
    get type() {
        return this.listType;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItemVector(itemIndex) {
        if (!this.validity.itemValid(itemIndex)) {
            return null;
        }
        const entryDataStartIndex = itemIndex * 2;
        const offset = Number(this.entryData[entryDataStartIndex]);
        const length = Number(this.entryData[entryDataStartIndex + 1]);
        return this.childData.slice(offset, length);
    }
    getItem(itemIndex) {
        const cachedItem = this.itemCache[itemIndex];
        if (cachedItem !== undefined) {
            return cachedItem;
        }
        const vector = this.getItemVector(itemIndex);
        if (!vector) {
            return null;
        }
        const item = new values_1.DuckDBListValue(vector.toArray());
        this.itemCache[itemIndex] = item;
        return item;
    }
    setItem(itemIndex, value) {
        this.itemCache[itemIndex] = value;
        if (this.parentList) {
            this.parentList.setItem(this.itemOffset + itemIndex, value);
        }
        else {
            this.validity.setItemValid(itemIndex, value != null);
        }
    }
    flush() {
        if (this.parentList) {
            this.parentList.flush();
            for (let i = 0; i < this.itemCount; i++) {
                this.itemCache[i] = undefined;
            }
        }
        else {
            // update entryData offset & lengths
            // calculate new child vector size (sum of all item lengths)
            let totalLength = 0;
            for (let itemIndex = 0; itemIndex < this._itemCount; itemIndex++) {
                const entryDataStartIndex = itemIndex * 2;
                this.entryData[entryDataStartIndex] = BigInt(totalLength);
                // ensure the cache is populated for all items
                const item = this.getItem(itemIndex);
                if (item) {
                    this.entryData[entryDataStartIndex + 1] = BigInt(item.items.length);
                    totalLength += item.items.length;
                }
                else {
                    this.entryData[entryDataStartIndex + 1] = 0n;
                }
            }
            // set new child vector size
            node_bindings_1.default.list_vector_set_size(this.vector, totalLength);
            // recreate childData after resize
            const child_vector = node_bindings_1.default.list_vector_get_child(this.vector);
            const child_vector_size = node_bindings_1.default.list_vector_get_size(this.vector);
            this.childData = DuckDBVector.create(child_vector, child_vector_size, this.listType.valueType);
            // set all childData items
            let childItemAbsoluteIndex = 0;
            for (let listIndex = 0; listIndex < this._itemCount; listIndex++) {
                const list = this.getItem(listIndex);
                if (list) {
                    for (let childItemRelativeIndex = 0; childItemRelativeIndex < list.items.length; childItemRelativeIndex++) {
                        this.childData.setItem(childItemAbsoluteIndex++, list.items[childItemRelativeIndex]);
                    }
                }
            }
            // copy childData to child vector
            this.childData.flush();
            // copy entryData to vector
            node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.entryData.buffer, this.entryData.byteOffset, this.entryData.byteLength);
            // flush validity
            this.validity.flush(this.vector);
        }
    }
    slice(offset, length) {
        const entryDataStartIndex = offset * 2;
        return new DuckDBListVector(this, this.listType, this.entryData.slice(entryDataStartIndex, entryDataStartIndex + length * 2), this.validity.slice(offset, length), this.vector, this.childData, offset, length);
    }
}
exports.DuckDBListVector = DuckDBListVector;
class DuckDBStructVector extends DuckDBVector {
    structType;
    _itemCount;
    entryVectors;
    validity;
    vector;
    constructor(structType, itemCount, entryVectors, validity, vector) {
        super();
        this.structType = structType;
        this._itemCount = itemCount;
        this.entryVectors = entryVectors;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(structType, vector, itemCount) {
        const entryCount = structType.entryCount;
        const entryVectors = [];
        for (let i = 0; i < entryCount; i++) {
            const child_vector = node_bindings_1.default.struct_vector_get_child(vector, i);
            entryVectors.push(DuckDBVector.create(child_vector, itemCount, structType.entryTypes[i]));
        }
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBStructVector(structType, itemCount, entryVectors, validity, vector);
    }
    get type() {
        return this.structType;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        if (!this.validity.itemValid(itemIndex)) {
            return null;
        }
        const entries = {};
        const entryCount = this.structType.entryCount;
        for (let i = 0; i < entryCount; i++) {
            entries[this.structType.entryNames[i]] =
                this.entryVectors[i].getItem(itemIndex);
        }
        return new values_1.DuckDBStructValue(entries);
    }
    getItemValue(itemIndex, entryIndex) {
        if (!this.validity.itemValid(itemIndex)) {
            return null;
        }
        return this.entryVectors[entryIndex].getItem(itemIndex);
    }
    setItem(itemIndex, value) {
        if (value != null) {
            const entryCount = this.structType.entryCount;
            for (let i = 0; i < entryCount; i++) {
                this.entryVectors[i].setItem(itemIndex, value.entries[this.structType.entryNames[i]]);
            }
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            const entryCount = this.structType.entryCount;
            for (let i = 0; i < entryCount; i++) {
                this.entryVectors[i].setItem(itemIndex, null);
            }
            this.validity.setItemValid(itemIndex, false);
        }
    }
    setItemValue(itemIndex, entryIndex, value) {
        return this.entryVectors[entryIndex].setItem(itemIndex, value);
    }
    flush() {
        for (const entryVector of this.entryVectors) {
            entryVector.flush();
        }
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBStructVector(this.structType, length, this.entryVectors.map((entryVector) => entryVector.slice(offset, length)), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBStructVector = DuckDBStructVector;
// MAP = LIST(STRUCT(key KEY_TYPE, value VALUE_TYPE))
class DuckDBMapVector extends DuckDBVector {
    mapType;
    listVector;
    constructor(mapType, listVector) {
        super();
        this.mapType = mapType;
        this.listVector = listVector;
    }
    static fromRawVector(mapType, vector, itemCount) {
        const listVectorType = new DuckDBType_1.DuckDBListType(new DuckDBType_1.DuckDBStructType(['key', 'value'], [mapType.keyType, mapType.valueType]));
        return new DuckDBMapVector(mapType, DuckDBListVector.fromRawVector(listVectorType, vector, itemCount));
    }
    get type() {
        return this.mapType;
    }
    get itemCount() {
        return this.listVector.itemCount;
    }
    getItem(itemIndex) {
        const itemVector = this.listVector.getItemVector(itemIndex);
        if (!itemVector) {
            return null;
        }
        if (!(itemVector instanceof DuckDBStructVector)) {
            throw new Error('item in map list vector is not a struct');
        }
        const entries = [];
        const itemEntryCount = itemVector.itemCount;
        for (let i = 0; i < itemEntryCount; i++) {
            const key = itemVector.getItemValue(i, 0);
            const value = itemVector.getItemValue(i, 1);
            entries.push({ key, value });
        }
        return new values_1.DuckDBMapValue(entries);
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.listVector.setItem(itemIndex, (0, values_1.listValue)(value.entries.map((entry) => (0, values_1.structValue)({ 'key': entry.key, 'value': entry.value }))));
        }
        else {
            this.listVector.setItem(itemIndex, null);
        }
    }
    flush() {
        this.listVector.flush();
    }
    slice(offset, length) {
        return new DuckDBMapVector(this.mapType, this.listVector.slice(offset, length));
    }
}
exports.DuckDBMapVector = DuckDBMapVector;
class DuckDBArrayVector extends DuckDBVector {
    arrayType;
    validity;
    vector;
    childData;
    _itemCount;
    constructor(arrayType, validity, vector, childData, itemCount) {
        super();
        this.arrayType = arrayType;
        this.validity = validity;
        this.vector = vector;
        this.childData = childData;
        this._itemCount = itemCount;
    }
    static fromRawVector(arrayType, vector, itemCount) {
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        const child_vector = node_bindings_1.default.array_vector_get_child(vector);
        const childItemsPerArray = DuckDBArrayVector.itemSize(arrayType) * arrayType.length;
        const childData = DuckDBVector.create(child_vector, itemCount * childItemsPerArray, arrayType.valueType);
        return new DuckDBArrayVector(arrayType, validity, vector, childData, itemCount);
    }
    static itemSize(arrayType) {
        if (arrayType.valueType instanceof DuckDBType_1.DuckDBArrayType) {
            return DuckDBArrayVector.itemSize(arrayType.valueType);
        }
        else {
            return 1;
        }
    }
    get type() {
        return this.arrayType;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        if (!this.validity.itemValid(itemIndex)) {
            return null;
        }
        return new values_1.DuckDBArrayValue(this.childData
            .slice(itemIndex * this.arrayType.length, this.arrayType.length)
            .toArray());
    }
    setItem(itemIndex, value) {
        if (value != null) {
            const startIndex = itemIndex * this.arrayType.length;
            for (let i = 0; i < this.arrayType.length; i++) {
                this.childData.setItem(startIndex + i, value.items[i]);
            }
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            const startIndex = itemIndex * this.arrayType.length;
            for (let i = 0; i < this.arrayType.length; i++) {
                this.childData.setItem(startIndex + i, null);
            }
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        this.childData.flush();
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBArrayVector(this.arrayType, this.validity.slice(offset, length), this.vector, this.childData.slice(offset * this.arrayType.length, length * this.arrayType.length), length);
    }
}
exports.DuckDBArrayVector = DuckDBArrayVector;
class DuckDBUUIDVector extends DuckDBVector {
    dataView;
    validity;
    vector;
    _itemCount;
    constructor(dataView, validity, vector, itemCount) {
        super();
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this._itemCount = itemCount;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * 16);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBUUIDVector(dataView, validity, vector, itemCount);
    }
    get type() {
        return DuckDBType_1.DuckDBUUIDType.instance;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? values_1.DuckDBUUIDValue.fromStoredHugeInt(getInt128(this.dataView, itemIndex * 16))
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            setInt128(this.dataView, itemIndex * 16, value.hugeint);
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.dataView.buffer, this.dataView.byteOffset, this.dataView.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBUUIDVector(new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 16, length * 16), this.validity.slice(offset, length), this.vector, length);
    }
}
exports.DuckDBUUIDVector = DuckDBUUIDVector;
// UNION = STRUCT with first entry named "tag"
class DuckDBUnionVector extends DuckDBVector {
    unionType;
    structVector;
    constructor(unionType, structVector) {
        super();
        this.unionType = unionType;
        this.structVector = structVector;
    }
    static fromRawVector(unionType, vector, itemCount) {
        const entryNames = ['tag'];
        const entryTypes = [DuckDBType_1.DuckDBUTinyIntType.instance];
        const memberCount = unionType.memberCount;
        for (let i = 0; i < memberCount; i++) {
            entryNames.push(unionType.memberTags[i]);
            entryTypes.push(unionType.memberTypes[i]);
        }
        const structVectorType = new DuckDBType_1.DuckDBStructType(entryNames, entryTypes);
        return new DuckDBUnionVector(unionType, DuckDBStructVector.fromRawVector(structVectorType, vector, itemCount));
    }
    get type() {
        return this.unionType;
    }
    get itemCount() {
        return this.structVector.itemCount;
    }
    getItem(itemIndex) {
        const tagValue = this.structVector.getItemValue(itemIndex, 0);
        if (tagValue == null) {
            return null;
        }
        const memberIndex = Number(tagValue);
        const tag = this.unionType.memberTags[memberIndex];
        const entryIndex = memberIndex + 1;
        const value = this.structVector.getItemValue(itemIndex, entryIndex);
        return new values_1.DuckDBUnionValue(tag, value);
    }
    setItem(itemIndex, value) {
        if (value != null) {
            const memberIndex = this.unionType.memberIndexForTag(value.tag);
            this.structVector.setItemValue(itemIndex, 0, memberIndex);
            const entryIndex = memberIndex + 1;
            this.structVector.setItemValue(itemIndex, entryIndex, value.value);
            for (let i = 1; i <= this.unionType.memberCount; i++) {
                if (i !== entryIndex) {
                    this.structVector.setItemValue(itemIndex, i, null);
                }
            }
        }
        else {
            for (let i = 0; i <= this.unionType.memberCount; i++) {
                this.structVector.setItemValue(itemIndex, i, null);
            }
        }
    }
    flush() {
        this.structVector.flush();
    }
    slice(offset, length) {
        return new DuckDBUnionVector(this.unionType, this.structVector.slice(offset, length));
    }
}
exports.DuckDBUnionVector = DuckDBUnionVector;
class DuckDBBitVector extends DuckDBVector {
    dataView;
    validity;
    vector;
    itemOffset;
    _itemCount;
    itemCache;
    itemCacheDirty;
    constructor(dataView, validity, vector, itemOffset, itemCount) {
        super();
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this.itemOffset = itemOffset;
        this._itemCount = itemCount;
        this.itemCache = [];
        this.itemCacheDirty = [];
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * 16);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBBitVector(dataView, validity, vector, 0, itemCount);
    }
    get type() {
        return DuckDBType_1.DuckDBBitType.instance;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        if (!this.validity.itemValid(itemIndex)) {
            return null;
        }
        const bytes = getStringBytes(this.dataView, itemIndex * 16);
        return bytes ? new values_1.DuckDBBitValue(bytes) : null;
    }
    setItem(itemIndex, value) {
        this.itemCache[itemIndex] = value;
        this.validity.setItemValid(itemIndex, value != null);
        this.itemCacheDirty[itemIndex] = true;
    }
    flush() {
        for (let itemIndex = 0; itemIndex < this._itemCount; itemIndex++) {
            if (this.itemCacheDirty[itemIndex]) {
                const cachedItem = this.itemCache[itemIndex];
                if (cachedItem !== undefined && cachedItem !== null) {
                    node_bindings_1.default.vector_assign_string_element_len(this.vector, this.itemOffset + itemIndex, cachedItem.data);
                }
                this.itemCacheDirty[itemIndex] = false;
            }
        }
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBBitVector(new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 16, length * 16), this.validity.slice(offset, length), this.vector, offset, length);
    }
}
exports.DuckDBBitVector = DuckDBBitVector;
class DuckDBTimeTZVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * BigUint64Array.BYTES_PER_ELEMENT);
        const items = new BigUint64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBTimeTZVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBTimeTZType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? values_1.DuckDBTimeTZValue.fromBits(this.items[itemIndex])
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value.bits;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBTimeTZVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBTimeTZVector = DuckDBTimeTZVector;
class DuckDBTimestampTZVector extends DuckDBVector {
    items;
    validity;
    vector;
    constructor(items, validity, vector) {
        super();
        this.items = items;
        this.validity = validity;
        this.vector = vector;
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * BigInt64Array.BYTES_PER_ELEMENT);
        const items = new BigInt64Array(data.buffer, data.byteOffset, itemCount);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBTimestampTZVector(items, validity, vector);
    }
    get type() {
        return DuckDBType_1.DuckDBTimestampTZType.instance;
    }
    get itemCount() {
        return this.items.length;
    }
    getItem(itemIndex) {
        return this.validity.itemValid(itemIndex)
            ? new values_1.DuckDBTimestampTZValue(this.items[itemIndex])
            : null;
    }
    setItem(itemIndex, value) {
        if (value != null) {
            this.items[itemIndex] = value.micros;
            this.validity.setItemValid(itemIndex, true);
        }
        else {
            this.validity.setItemValid(itemIndex, false);
        }
    }
    flush() {
        node_bindings_1.default.copy_data_to_vector(this.vector, 0, this.items.buffer, this.items.byteOffset, this.items.byteLength);
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBTimestampTZVector(this.items.slice(offset, offset + length), this.validity.slice(offset, length), this.vector);
    }
}
exports.DuckDBTimestampTZVector = DuckDBTimestampTZVector;
class DuckDBBigNumVector extends DuckDBVector {
    dataView;
    validity;
    vector;
    itemOffset;
    _itemCount;
    itemCache;
    itemCacheDirty;
    constructor(dataView, validity, vector, itemOffset, itemCount) {
        super();
        this.dataView = dataView;
        this.validity = validity;
        this.vector = vector;
        this.itemOffset = itemOffset;
        this._itemCount = itemCount;
        this.itemCache = [];
        this.itemCacheDirty = [];
    }
    static fromRawVector(vector, itemCount) {
        const data = vectorData(vector, itemCount * 16);
        const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const validity = DuckDBValidity.fromVector(vector, itemCount);
        return new DuckDBBigNumVector(dataView, validity, vector, 0, itemCount);
    }
    get type() {
        return DuckDBType_1.DuckDBBigNumType.instance;
    }
    get itemCount() {
        return this._itemCount;
    }
    getItem(itemIndex) {
        if (!this.validity.itemValid(itemIndex)) {
            return null;
        }
        const bytes = getStringBytes(this.dataView, itemIndex * 16);
        return bytes ? getBigNumFromBytes(bytes) : null;
    }
    setItem(itemIndex, value) {
        this.itemCache[itemIndex] = value;
        this.validity.setItemValid(itemIndex, value != null);
        this.itemCacheDirty[itemIndex] = true;
    }
    flush() {
        for (let itemIndex = 0; itemIndex < this._itemCount; itemIndex++) {
            if (this.itemCacheDirty[itemIndex]) {
                const cachedItem = this.itemCache[itemIndex];
                if (cachedItem !== undefined && cachedItem !== null) {
                    node_bindings_1.default.vector_assign_string_element_len(this.vector, this.itemOffset + itemIndex, getBytesFromBigNum(cachedItem));
                }
                this.itemCacheDirty[itemIndex] = false;
            }
        }
        this.validity.flush(this.vector);
    }
    slice(offset, length) {
        return new DuckDBBigNumVector(new DataView(this.dataView.buffer, this.dataView.byteOffset + offset * 16, length * 16), this.validity.slice(offset, length), this.vector, offset, length);
    }
}
exports.DuckDBBigNumVector = DuckDBBigNumVector;
