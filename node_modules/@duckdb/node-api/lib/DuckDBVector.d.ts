import duckdb from '@duckdb/node-bindings';
import { Duck<PERSON><PERSON><PERSON>yType, DuckDBBigIntType, DuckDBBigNumType, DuckDBBitType, DuckDBBlobType, DuckDBBooleanType, DuckDBDateType, DuckDBDecimalType, DuckDBDoubleType, DuckDBEnumType, DuckDBFloatType, DuckDBHugeIntType, DuckDBIntegerType, DuckDBIntervalType, DuckDBListType, DuckDBMapType, DuckDBSmallIntType, DuckDBStructType, DuckDBTimeTZType, DuckDBTimeType, DuckDBTimestampMillisecondsType, DuckDBTimestampNanosecondsType, DuckDBTimestampSecondsType, DuckDBTimestampTZType, DuckDBTimestampType, DuckDBTinyIntType, DuckDBType, DuckDBUBigIntType, DuckDBUHugeIntType, DuckD<PERSON><PERSON><PERSON>gerType, DuckDBUSmallIntType, Duck<PERSON><PERSON><PERSON>inyIntType, Duck<PERSON><PERSON><PERSON><PERSON>DType, Duck<PERSON><PERSON><PERSON>nionType, DuckD<PERSON><PERSON><PERSON><PERSON>harType } from './DuckDBType';
import { Duck<PERSON><PERSON><PERSON><PERSON>Value, <PERSON><PERSON><PERSON><PERSON>Value, DuckDBBlobValue, DuckDBDateValue, DuckDBDecimalValue, DuckDBIntervalValue, DuckDBListValue, DuckDBMapValue, DuckDBStructValue, DuckDBTimeTZValue, DuckDBTimeValue, DuckDBTimestampMillisecondsValue, DuckDBTimestampNanosecondsValue, DuckDBTimestampSecondsValue, DuckDBTimestampTZValue, DuckDBTimestampValue, DuckDBUUIDValue, DuckDBUnionValue, DuckDBValue } from './values';
declare class DuckDBValidity {
    private data;
    private readonly offset;
    private readonly itemCount;
    private constructor();
    static fromVector(vector: duckdb.Vector, itemCount: number): DuckDBValidity;
    itemValid(itemIndex: number): boolean;
    setItemValid(itemIndex: number, valid: boolean): void;
    flush(vector: duckdb.Vector): void;
    slice(offset: number, itemCount: number): DuckDBValidity;
}
export declare abstract class DuckDBVector<TValue extends DuckDBValue = DuckDBValue> {
    static standardSize(): number;
    static create(vector: duckdb.Vector, itemCount: number, knownType?: DuckDBType): DuckDBVector;
    abstract get type(): DuckDBType;
    abstract get itemCount(): number;
    abstract getItem(itemIndex: number): TValue | null;
    abstract setItem(itemIndex: number, value: TValue | null): void;
    abstract flush(): void;
    abstract slice(offset: number, length: number): DuckDBVector<TValue>;
    toArray(): (TValue | null)[];
}
export declare class DuckDBBooleanVector extends DuckDBVector<boolean> {
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly _itemCount;
    constructor(dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemCount: number);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBBooleanVector;
    get type(): DuckDBBooleanType;
    get itemCount(): number;
    getItem(itemIndex: number): boolean | null;
    setItem(itemIndex: number, value: boolean | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBBooleanVector;
}
export declare class DuckDBTinyIntVector extends DuckDBVector<number> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: Int8Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBTinyIntVector;
    get type(): DuckDBTinyIntType;
    get itemCount(): number;
    getItem(itemIndex: number): number | null;
    setItem(itemIndex: number, value: number | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBTinyIntVector;
}
export declare class DuckDBSmallIntVector extends DuckDBVector<number> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: Int16Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBSmallIntVector;
    get type(): DuckDBSmallIntType;
    get itemCount(): number;
    getItem(itemIndex: number): number | null;
    setItem(itemIndex: number, value: number | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBSmallIntVector;
}
export declare class DuckDBIntegerVector extends DuckDBVector<number> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: Int32Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBIntegerVector;
    get type(): DuckDBIntegerType;
    get itemCount(): number;
    getItem(itemIndex: number): number | null;
    setItem(itemIndex: number, value: number | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBIntegerVector;
}
export declare class DuckDBBigIntVector extends DuckDBVector<bigint> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: BigInt64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBBigIntVector;
    get type(): DuckDBBigIntType;
    get itemCount(): number;
    getItem(itemIndex: number): bigint | null;
    setItem(itemIndex: number, value: bigint | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBBigIntVector;
}
export declare class DuckDBUTinyIntVector extends DuckDBVector<number> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: Uint8Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBUTinyIntVector;
    get type(): DuckDBUTinyIntType;
    get itemCount(): number;
    getItem(itemIndex: number): number | null;
    setItem(itemIndex: number, value: number | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBUTinyIntVector;
}
export declare class DuckDBUSmallIntVector extends DuckDBVector<number> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: Uint16Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBUSmallIntVector;
    get type(): DuckDBUSmallIntType;
    get itemCount(): number;
    getItem(itemIndex: number): number | null;
    setItem(itemIndex: number, value: number | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBUSmallIntVector;
}
export declare class DuckDBUIntegerVector extends DuckDBVector<number> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: Uint32Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBUIntegerVector;
    get type(): DuckDBUIntegerType;
    get itemCount(): number;
    getItem(itemIndex: number): number | null;
    setItem(itemIndex: number, value: number | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBUIntegerVector;
}
export declare class DuckDBUBigIntVector extends DuckDBVector<bigint> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: BigUint64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBUBigIntVector;
    get type(): DuckDBUBigIntType;
    get itemCount(): number;
    getItem(itemIndex: number): bigint | null;
    setItem(itemIndex: number, value: bigint | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBUBigIntVector;
}
export declare class DuckDBFloatVector extends DuckDBVector<number> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: Float32Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBFloatVector;
    get type(): DuckDBFloatType;
    get itemCount(): number;
    getItem(itemIndex: number): number | null;
    setItem(itemIndex: number, value: number | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBFloatVector;
}
export declare class DuckDBDoubleVector extends DuckDBVector<number> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: Float64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBDoubleVector;
    get type(): DuckDBDoubleType;
    get itemCount(): number;
    getItem(itemIndex: number): number | null;
    setItem(itemIndex: number, value: number | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBDoubleVector;
}
export declare class DuckDBTimestampVector extends DuckDBVector<DuckDBTimestampValue> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: BigInt64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBTimestampVector;
    get type(): DuckDBTimestampType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBTimestampValue | null;
    setItem(itemIndex: number, value: DuckDBTimestampValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBTimestampVector;
}
export declare class DuckDBDateVector extends DuckDBVector<DuckDBDateValue> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: Int32Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBDateVector;
    get type(): DuckDBDateType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBDateValue | null;
    setItem(itemIndex: number, value: DuckDBDateValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBDateVector;
}
export declare class DuckDBTimeVector extends DuckDBVector<DuckDBTimeValue> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: BigInt64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBTimeVector;
    get type(): DuckDBTimeType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBTimeValue | null;
    setItem(itemIndex: number, value: DuckDBTimeValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBTimeVector;
}
export declare class DuckDBIntervalVector extends DuckDBVector<DuckDBIntervalValue> {
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly _itemCount;
    constructor(dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemCount: number);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBIntervalVector;
    get type(): DuckDBIntervalType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBIntervalValue | null;
    setItem(itemIndex: number, value: DuckDBIntervalValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBIntervalVector;
}
export declare class DuckDBHugeIntVector extends DuckDBVector<bigint> {
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly _itemCount;
    constructor(dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemCount: number);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBHugeIntVector;
    get type(): DuckDBHugeIntType;
    get itemCount(): number;
    getItem(itemIndex: number): bigint | null;
    getDouble(itemIndex: number): number | null;
    setItem(itemIndex: number, value: bigint | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBHugeIntVector;
}
export declare class DuckDBUHugeIntVector extends DuckDBVector<bigint> {
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly _itemCount;
    constructor(dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemCount: number);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBUHugeIntVector;
    get type(): DuckDBUHugeIntType;
    get itemCount(): number;
    getItem(itemIndex: number): bigint | null;
    getDouble(itemIndex: number): number | null;
    setItem(itemIndex: number, value: bigint | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBUHugeIntVector;
}
export declare class DuckDBVarCharVector extends DuckDBVector<string> {
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly itemOffset;
    private readonly _itemCount;
    private readonly itemCache;
    private readonly itemCacheDirty;
    constructor(dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemOffset: number, itemCount: number);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBVarCharVector;
    get type(): DuckDBVarCharType;
    get itemCount(): number;
    getItem(itemIndex: number): string | null;
    setItem(itemIndex: number, value: string | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBVarCharVector;
}
export declare class DuckDBBlobVector extends DuckDBVector<DuckDBBlobValue> {
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly itemOffset;
    private readonly _itemCount;
    private readonly itemCache;
    private readonly itemCacheDirty;
    constructor(dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemOffset: number, itemCount: number);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBBlobVector;
    get type(): DuckDBBlobType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBBlobValue | null;
    setItem(itemIndex: number, value: DuckDBBlobValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBBlobVector;
}
export declare class DuckDBDecimal16Vector extends DuckDBVector<DuckDBDecimalValue> {
    private readonly decimalType;
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly _itemCount;
    constructor(decimalType: DuckDBDecimalType, dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemCount: number);
    static fromRawVector(decimalType: DuckDBDecimalType, vector: duckdb.Vector, itemCount: number): DuckDBDecimal16Vector;
    get type(): DuckDBDecimalType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBDecimalValue | null;
    getScaledValue(itemIndex: number): number | null;
    setItem(itemIndex: number, value: DuckDBDecimalValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBDecimal16Vector;
}
export declare class DuckDBDecimal32Vector extends DuckDBVector<DuckDBDecimalValue> {
    private readonly decimalType;
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly _itemCount;
    constructor(decimalType: DuckDBDecimalType, dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemCount: number);
    static fromRawVector(decimalType: DuckDBDecimalType, vector: duckdb.Vector, itemCount: number): DuckDBDecimal32Vector;
    get type(): DuckDBDecimalType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBDecimalValue | null;
    getScaledValue(itemIndex: number): number | null;
    setItem(itemIndex: number, value: DuckDBDecimalValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBDecimal32Vector;
}
export declare class DuckDBDecimal64Vector extends DuckDBVector<DuckDBDecimalValue> {
    private readonly decimalType;
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly _itemCount;
    constructor(decimalType: DuckDBDecimalType, dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemCount: number);
    static fromRawVector(decimalType: DuckDBDecimalType, vector: duckdb.Vector, itemCount: number): DuckDBDecimal64Vector;
    get type(): DuckDBDecimalType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBDecimalValue | null;
    getScaledValue(itemIndex: number): bigint | null;
    setItem(itemIndex: number, value: DuckDBDecimalValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBDecimal64Vector;
}
export declare class DuckDBDecimal128Vector extends DuckDBVector<DuckDBDecimalValue> {
    private readonly decimalType;
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly _itemCount;
    constructor(decimalType: DuckDBDecimalType, dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemCount: number);
    static fromRawVector(decimalType: DuckDBDecimalType, vector: duckdb.Vector, itemCount: number): DuckDBDecimal128Vector;
    get type(): DuckDBDecimalType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBDecimalValue | null;
    getScaledValue(itemIndex: number): bigint | null;
    setItem(itemIndex: number, value: DuckDBDecimalValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBDecimal128Vector;
}
export declare class DuckDBTimestampSecondsVector extends DuckDBVector<DuckDBTimestampSecondsValue> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: BigInt64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBTimestampSecondsVector;
    get type(): DuckDBTimestampSecondsType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBTimestampSecondsValue | null;
    setItem(itemIndex: number, value: DuckDBTimestampSecondsValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBTimestampSecondsVector;
}
export declare class DuckDBTimestampMillisecondsVector extends DuckDBVector<DuckDBTimestampMillisecondsValue> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: BigInt64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBTimestampMillisecondsVector;
    get type(): DuckDBTimestampMillisecondsType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBTimestampMillisecondsValue | null;
    setItem(itemIndex: number, value: DuckDBTimestampMillisecondsValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBTimestampMillisecondsVector;
}
export declare class DuckDBTimestampNanosecondsVector extends DuckDBVector<DuckDBTimestampNanosecondsValue> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: BigInt64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBTimestampNanosecondsVector;
    get type(): DuckDBTimestampNanosecondsType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBTimestampNanosecondsValue | null;
    setItem(itemIndex: number, value: DuckDBTimestampNanosecondsValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBTimestampNanosecondsVector;
}
export declare class DuckDBEnum8Vector extends DuckDBVector<string> {
    private readonly enumType;
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(enumType: DuckDBEnumType, items: Uint8Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(enumType: DuckDBEnumType, vector: duckdb.Vector, itemCount: number): DuckDBEnum8Vector;
    get type(): DuckDBEnumType;
    get itemCount(): number;
    getItem(itemIndex: number): string | null;
    setItem(itemIndex: number, value: string | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBEnum8Vector;
}
export declare class DuckDBEnum16Vector extends DuckDBVector<string> {
    private readonly enumType;
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(enumType: DuckDBEnumType, items: Uint16Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(enumType: DuckDBEnumType, vector: duckdb.Vector, itemCount: number): DuckDBEnum16Vector;
    get type(): DuckDBEnumType;
    get itemCount(): number;
    getItem(itemIndex: number): string | null;
    setItem(itemIndex: number, value: string | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBEnum16Vector;
}
export declare class DuckDBEnum32Vector extends DuckDBVector<string> {
    private readonly enumType;
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(enumType: DuckDBEnumType, items: Uint32Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(enumType: DuckDBEnumType, vector: duckdb.Vector, itemCount: number): DuckDBEnum32Vector;
    get type(): DuckDBEnumType;
    get itemCount(): number;
    getItem(itemIndex: number): string | null;
    setItem(itemIndex: number, value: string | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBEnum32Vector;
}
export declare class DuckDBListVector extends DuckDBVector<DuckDBListValue> {
    private readonly parentList;
    private readonly listType;
    private readonly entryData;
    private readonly validity;
    private readonly vector;
    private childData;
    private readonly itemOffset;
    private readonly _itemCount;
    private readonly itemCache;
    constructor(parentList: DuckDBListVector | null, listType: DuckDBListType, entryData: BigUint64Array, validity: DuckDBValidity, vector: duckdb.Vector, childData: DuckDBVector, itemOffset: number, itemCount: number);
    static fromRawVector(listType: DuckDBListType, vector: duckdb.Vector, itemCount: number): DuckDBListVector;
    get type(): DuckDBListType;
    get itemCount(): number;
    getItemVector(itemIndex: number): DuckDBVector | null;
    getItem(itemIndex: number): DuckDBListValue | null;
    setItem(itemIndex: number, value: DuckDBListValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBListVector;
}
export declare class DuckDBStructVector extends DuckDBVector<DuckDBStructValue> {
    private readonly structType;
    private readonly _itemCount;
    private readonly entryVectors;
    private readonly validity;
    private readonly vector;
    constructor(structType: DuckDBStructType, itemCount: number, entryVectors: readonly DuckDBVector[], validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(structType: DuckDBStructType, vector: duckdb.Vector, itemCount: number): DuckDBStructVector;
    get type(): DuckDBStructType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBStructValue | null;
    getItemValue(itemIndex: number, entryIndex: number): DuckDBValue | null;
    setItem(itemIndex: number, value: DuckDBStructValue | null): void;
    setItemValue(itemIndex: number, entryIndex: number, value: DuckDBValue): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBStructVector;
}
export declare class DuckDBMapVector extends DuckDBVector<DuckDBMapValue> {
    private readonly mapType;
    private readonly listVector;
    constructor(mapType: DuckDBMapType, listVector: DuckDBListVector);
    static fromRawVector(mapType: DuckDBMapType, vector: duckdb.Vector, itemCount: number): DuckDBMapVector;
    get type(): DuckDBMapType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBMapValue | null;
    setItem(itemIndex: number, value: DuckDBMapValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBMapVector;
}
export declare class DuckDBArrayVector extends DuckDBVector<DuckDBArrayValue> {
    private readonly arrayType;
    private readonly validity;
    private readonly vector;
    private readonly childData;
    private readonly _itemCount;
    constructor(arrayType: DuckDBArrayType, validity: DuckDBValidity, vector: duckdb.Vector, childData: DuckDBVector, itemCount: number);
    static fromRawVector(arrayType: DuckDBArrayType, vector: duckdb.Vector, itemCount: number): DuckDBArrayVector;
    private static itemSize;
    get type(): DuckDBArrayType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBArrayValue | null;
    setItem(itemIndex: number, value: DuckDBArrayValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBArrayVector;
}
export declare class DuckDBUUIDVector extends DuckDBVector<DuckDBUUIDValue> {
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly _itemCount;
    constructor(dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemCount: number);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBUUIDVector;
    get type(): DuckDBUUIDType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBUUIDValue | null;
    setItem(itemIndex: number, value: DuckDBUUIDValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBUUIDVector;
}
export declare class DuckDBUnionVector extends DuckDBVector<DuckDBUnionValue> {
    private readonly unionType;
    private readonly structVector;
    constructor(unionType: DuckDBUnionType, structVector: DuckDBStructVector);
    static fromRawVector(unionType: DuckDBUnionType, vector: duckdb.Vector, itemCount: number): DuckDBUnionVector;
    get type(): DuckDBUnionType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBUnionValue | null;
    setItem(itemIndex: number, value: DuckDBUnionValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBUnionVector;
}
export declare class DuckDBBitVector extends DuckDBVector<DuckDBBitValue> {
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly itemOffset;
    private readonly _itemCount;
    private readonly itemCache;
    private readonly itemCacheDirty;
    constructor(dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemOffset: number, itemCount: number);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBBitVector;
    get type(): DuckDBBitType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBBitValue | null;
    setItem(itemIndex: number, value: DuckDBBitValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBBitVector;
}
export declare class DuckDBTimeTZVector extends DuckDBVector<DuckDBTimeTZValue> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: BigUint64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBTimeTZVector;
    get type(): DuckDBTimeTZType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBTimeTZValue | null;
    setItem(itemIndex: number, value: DuckDBTimeTZValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBTimeTZVector;
}
export declare class DuckDBTimestampTZVector extends DuckDBVector<DuckDBTimestampTZValue> {
    private readonly items;
    private readonly validity;
    private readonly vector;
    constructor(items: BigInt64Array, validity: DuckDBValidity, vector: duckdb.Vector);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBTimestampTZVector;
    get type(): DuckDBTimestampTZType;
    get itemCount(): number;
    getItem(itemIndex: number): DuckDBTimestampTZValue | null;
    setItem(itemIndex: number, value: DuckDBTimestampTZValue | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBTimestampTZVector;
}
export declare class DuckDBBigNumVector extends DuckDBVector<bigint> {
    private readonly dataView;
    private readonly validity;
    private readonly vector;
    private readonly itemOffset;
    private readonly _itemCount;
    private readonly itemCache;
    private readonly itemCacheDirty;
    constructor(dataView: DataView, validity: DuckDBValidity, vector: duckdb.Vector, itemOffset: number, itemCount: number);
    static fromRawVector(vector: duckdb.Vector, itemCount: number): DuckDBBigNumVector;
    get type(): DuckDBBigNumType;
    get itemCount(): number;
    getItem(itemIndex: number): bigint | null;
    setItem(itemIndex: number, value: bigint | null): void;
    flush(): void;
    slice(offset: number, length: number): DuckDBBigNumVector;
}
export {};
