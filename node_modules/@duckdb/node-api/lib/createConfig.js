"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createConfig = createConfig;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
function createConfig(options) {
    const config = node_bindings_1.default.create_config();
    // Set the default duckdb_api value for the api. Can be overridden.
    node_bindings_1.default.set_config(config, 'duckdb_api', 'node-neo-api');
    if (options) {
        for (const optionName in options) {
            const optionValue = String(options[optionName]);
            node_bindings_1.default.set_config(config, optionName, optionValue);
        }
    }
    return config;
}
