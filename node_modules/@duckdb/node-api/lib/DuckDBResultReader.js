"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBResultReader = void 0;
const convertColumnsFromChunks_1 = require("./convertColumnsFromChunks");
const convertColumnsObjectFromChunks_1 = require("./convertColumnsObjectFromChunks");
const convertRowObjectsFromChunks_1 = require("./convertRowObjectsFromChunks");
const convertRowsFromChunks_1 = require("./convertRowsFromChunks");
const getColumnsFromChunks_1 = require("./getColumnsFromChunks");
const getColumnsObjectFromChunks_1 = require("./getColumnsObjectFromChunks");
const getRowObjectsFromChunks_1 = require("./getRowObjectsFromChunks");
const getRowsFromChunks_1 = require("./getRowsFromChunks");
const JSDuckDBValueConverter_1 = require("./JSDuckDBValueConverter");
const JsonDuckDBValueConverter_1 = require("./JsonDuckDBValueConverter");
class DuckDBResultReader {
    result;
    chunks;
    chunkSizeRuns;
    currentRowCount_;
    done_;
    constructor(result) {
        this.result = result;
        this.chunks = [];
        this.chunkSizeRuns = [];
        this.currentRowCount_ = 0;
        this.done_ = false;
    }
    get returnType() {
        return this.result.returnType;
    }
    get statementType() {
        return this.result.statementType;
    }
    get columnCount() {
        return this.result.columnCount;
    }
    columnName(columnIndex) {
        return this.result.columnName(columnIndex);
    }
    columnNames() {
        return this.result.columnNames();
    }
    deduplicatedColumnNames() {
        return this.result.deduplicatedColumnNames();
    }
    columnTypeId(columnIndex) {
        return this.result.columnTypeId(columnIndex);
    }
    columnLogicalType(columnIndex) {
        return this.result.columnLogicalType(columnIndex);
    }
    columnType(columnIndex) {
        return this.result.columnType(columnIndex);
    }
    columnTypeJson(columnIndex) {
        return this.result.columnTypeJson(columnIndex);
    }
    columnTypes() {
        return this.result.columnTypes();
    }
    columnTypesJson() {
        return this.result.columnTypesJson();
    }
    columnNamesAndTypesJson() {
        return this.result.columnNamesAndTypesJson();
    }
    columnNameAndTypeObjectsJson() {
        return this.result.columnNameAndTypeObjectsJson();
    }
    get rowsChanged() {
        return this.result.rowsChanged;
    }
    /** Total number of rows read so far. Call `readAll` or `readUntil` to read rows. */
    get currentRowCount() {
        return this.currentRowCount_;
    }
    /** Whether reading is done, that is, there are no more rows to read. */
    get done() {
        return this.done_;
    }
    /**
     * Returns the value for the given column and row. Both are zero-indexed.
     *
     * Will return an error if `rowIndex` is greater than `currentRowCount`.
     */
    value(columnIndex, rowIndex) {
        if (this.currentRowCount_ === 0) {
            throw Error(`No rows have been read`);
        }
        let chunkIndex = 0;
        let currentRowIndex = rowIndex;
        // Find which run of chunks our row is in.
        // Since chunkSizeRuns shouldn't ever be longer than 2, this should be O(1).
        for (const run of this.chunkSizeRuns) {
            if (currentRowIndex < run.rowCount) {
                // The row we're looking for is in this run.
                // Calculate the chunk index and the row index in that chunk.
                chunkIndex += Math.floor(currentRowIndex / run.chunkSize);
                const rowIndexInChunk = currentRowIndex % run.chunkSize;
                const chunk = this.chunks[chunkIndex];
                return chunk.getColumnVector(columnIndex).getItem(rowIndexInChunk);
            }
            // The row we're looking for is not in this run.
            // Update our counts for this run and move to the next one.
            chunkIndex += run.chunkCount;
            currentRowIndex -= run.rowCount;
        }
        // We didn't find our row. It must have been out of range.
        throw Error(`Row index ${rowIndex} requested, but only ${this.currentRowCount_} row have been read so far.`);
    }
    /** Read all rows. */
    async readAll() {
        return this.fetchChunks();
    }
    /**
     * Read rows until at least the given target row count has been met.
     *
     * Note that the resulting row count could be greater than the target, since rows are read in chunks, typically of 2048 rows each.
     */
    async readUntil(targetRowCount) {
        return this.fetchChunks(targetRowCount);
    }
    async fetchChunks(targetRowCount) {
        while (!(this.done_ ||
            (targetRowCount !== undefined &&
                this.currentRowCount_ >= targetRowCount))) {
            const chunk = await this.result.fetchChunk();
            if (chunk && chunk.rowCount > 0) {
                this.updateChunkSizeRuns(chunk);
                this.chunks.push(chunk);
                this.currentRowCount_ += chunk.rowCount;
            }
            else {
                this.done_ = true;
            }
        }
    }
    updateChunkSizeRuns(chunk) {
        if (this.chunkSizeRuns.length > 0) {
            const lastRun = this.chunkSizeRuns[this.chunkSizeRuns.length - 1];
            if (lastRun.chunkSize === chunk.rowCount) {
                // If the new batch is the same size as the last one, just update our last run.
                lastRun.chunkCount += 1;
                lastRun.rowCount += lastRun.chunkSize;
                return;
            }
        }
        // If this is our first batch, or it's a different size, create a new run.
        this.chunkSizeRuns.push({
            chunkCount: 1,
            chunkSize: chunk.rowCount,
            rowCount: chunk.rowCount,
        });
    }
    getColumns() {
        return (0, getColumnsFromChunks_1.getColumnsFromChunks)(this.chunks);
    }
    convertColumns(converter) {
        return (0, convertColumnsFromChunks_1.convertColumnsFromChunks)(this.chunks, converter);
    }
    getColumnsJS() {
        return this.convertColumns(JSDuckDBValueConverter_1.JSDuckDBValueConverter);
    }
    getColumnsJson() {
        return this.convertColumns(JsonDuckDBValueConverter_1.JsonDuckDBValueConverter);
    }
    getColumnsObject() {
        return (0, getColumnsObjectFromChunks_1.getColumnsObjectFromChunks)(this.chunks, this.deduplicatedColumnNames());
    }
    convertColumnsObject(converter) {
        return (0, convertColumnsObjectFromChunks_1.convertColumnsObjectFromChunks)(this.chunks, this.deduplicatedColumnNames(), converter);
    }
    getColumnsObjectJS() {
        return this.convertColumnsObject(JSDuckDBValueConverter_1.JSDuckDBValueConverter);
    }
    getColumnsObjectJson() {
        return this.convertColumnsObject(JsonDuckDBValueConverter_1.JsonDuckDBValueConverter);
    }
    getRows() {
        return (0, getRowsFromChunks_1.getRowsFromChunks)(this.chunks);
    }
    convertRows(converter) {
        return (0, convertRowsFromChunks_1.convertRowsFromChunks)(this.chunks, converter);
    }
    getRowsJS() {
        return this.convertRows(JSDuckDBValueConverter_1.JSDuckDBValueConverter);
    }
    getRowsJson() {
        return this.convertRows(JsonDuckDBValueConverter_1.JsonDuckDBValueConverter);
    }
    getRowObjects() {
        return (0, getRowObjectsFromChunks_1.getRowObjectsFromChunks)(this.chunks, this.deduplicatedColumnNames());
    }
    convertRowObjects(converter) {
        return (0, convertRowObjectsFromChunks_1.convertRowObjectsFromChunks)(this.chunks, this.deduplicatedColumnNames(), converter);
    }
    getRowObjectsJS() {
        return this.convertRowObjects(JSDuckDBValueConverter_1.JSDuckDBValueConverter);
    }
    getRowObjectsJson() {
        return this.convertRowObjects(JsonDuckDBValueConverter_1.JsonDuckDBValueConverter);
    }
}
exports.DuckDBResultReader = DuckDBResultReader;
