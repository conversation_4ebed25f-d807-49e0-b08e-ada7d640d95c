"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBAppender = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const createValue_1 = require("./createValue");
const DuckDBLogicalType_1 = require("./DuckDBLogicalType");
const DuckDBType_1 = require("./DuckDBType");
const typeForValue_1 = require("./typeForValue");
const values_1 = require("./values");
class DuckDBAppender {
    appender;
    constructor(appender) {
        this.appender = appender;
    }
    closeSync() {
        node_bindings_1.default.appender_close_sync(this.appender);
    }
    flushSync() {
        node_bindings_1.default.appender_flush_sync(this.appender);
    }
    get columnCount() {
        return node_bindings_1.default.appender_column_count(this.appender);
    }
    columnType(columnIndex) {
        return DuckDBLogicalType_1.DuckDBLogicalType.create(node_bindings_1.default.appender_column_type(this.appender, columnIndex)).asType();
    }
    endRow() {
        node_bindings_1.default.appender_end_row(this.appender);
    }
    appendDefault() {
        node_bindings_1.default.append_default(this.appender);
    }
    appendBoolean(value) {
        node_bindings_1.default.append_bool(this.appender, value);
    }
    appendTinyInt(value) {
        node_bindings_1.default.append_int8(this.appender, value);
    }
    appendSmallInt(value) {
        node_bindings_1.default.append_int16(this.appender, value);
    }
    appendInteger(value) {
        node_bindings_1.default.append_int32(this.appender, value);
    }
    appendBigInt(value) {
        node_bindings_1.default.append_int64(this.appender, value);
    }
    appendHugeInt(value) {
        node_bindings_1.default.append_hugeint(this.appender, value);
    }
    appendUTinyInt(value) {
        node_bindings_1.default.append_uint8(this.appender, value);
    }
    appendUSmallInt(value) {
        node_bindings_1.default.append_uint16(this.appender, value);
    }
    appendUInteger(value) {
        node_bindings_1.default.append_uint32(this.appender, value);
    }
    appendUBigInt(value) {
        node_bindings_1.default.append_uint64(this.appender, value);
    }
    appendUHugeInt(value) {
        node_bindings_1.default.append_uhugeint(this.appender, value);
    }
    appendDecimal(value) {
        // The width and scale of the DECIMAL type here aren't actually used.
        this.appendValue(value, (0, DuckDBType_1.DECIMAL)(value.width, value.scale));
    }
    appendFloat(value) {
        node_bindings_1.default.append_float(this.appender, value);
    }
    appendDouble(value) {
        node_bindings_1.default.append_double(this.appender, value);
    }
    appendDate(value) {
        node_bindings_1.default.append_date(this.appender, value);
    }
    appendTime(value) {
        node_bindings_1.default.append_time(this.appender, value);
    }
    appendTimeTZ(value) {
        this.appendValue(value, DuckDBType_1.TIMETZ);
    }
    appendTimestamp(value) {
        node_bindings_1.default.append_timestamp(this.appender, value);
    }
    appendTimestampTZ(value) {
        this.appendValue(value, DuckDBType_1.TIMESTAMPTZ);
    }
    appendTimestampSeconds(value) {
        this.appendValue(value, DuckDBType_1.TIMESTAMP_S);
    }
    appendTimestampMilliseconds(value) {
        this.appendValue(value, DuckDBType_1.TIMESTAMP_MS);
    }
    appendTimestampNanoseconds(value) {
        this.appendValue(value, DuckDBType_1.TIMESTAMP_NS);
    }
    appendInterval(value) {
        node_bindings_1.default.append_interval(this.appender, value);
    }
    appendVarchar(value) {
        node_bindings_1.default.append_varchar(this.appender, value);
    }
    appendBlob(value) {
        node_bindings_1.default.append_blob(this.appender, value);
    }
    appendEnum(value, type) {
        this.appendValue(value, type);
    }
    appendList(value, type) {
        this.appendValue(value instanceof values_1.DuckDBListValue ? value : (0, values_1.listValue)(value), type);
    }
    appendStruct(value, type) {
        this.appendValue(value instanceof values_1.DuckDBStructValue ? value : (0, values_1.structValue)(value), type);
    }
    appendMap(value, type) {
        this.appendValue(value, type);
    }
    appendArray(value, type) {
        this.appendValue(value instanceof values_1.DuckDBArrayValue ? value : (0, values_1.arrayValue)(value), type);
    }
    appendUnion(value, type) {
        this.appendValue(value, type);
    }
    appendUUID(value) {
        this.appendValue(value, DuckDBType_1.UUID);
    }
    appendBit(value) {
        this.appendValue(value, DuckDBType_1.BIT);
    }
    appendBigNum(value) {
        this.appendValue(value, DuckDBType_1.BIGNUM);
    }
    appendNull() {
        node_bindings_1.default.append_null(this.appender);
    }
    appendValue(value, type) {
        node_bindings_1.default.append_value(this.appender, (0, createValue_1.createValue)(type ? type : (0, typeForValue_1.typeForValue)(value), value));
    }
    appendDataChunk(dataChunk) {
        node_bindings_1.default.append_data_chunk(this.appender, dataChunk.chunk);
    }
}
exports.DuckDBAppender = DuckDBAppender;
