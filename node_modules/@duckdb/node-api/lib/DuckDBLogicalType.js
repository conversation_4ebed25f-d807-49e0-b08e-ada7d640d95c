"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuckDBUnionLogicalType = exports.DuckDBArrayLogicalType = exports.DuckDBMapLogicalType = exports.DuckDBStructLogicalType = exports.DuckDBListLogicalType = exports.DuckDBEnumLogicalType = exports.DuckDBDecimalLogicalType = exports.DuckDBLogicalType = void 0;
const node_bindings_1 = __importDefault(require("@duckdb/node-bindings"));
const DuckDBType_1 = require("./DuckDBType");
const DuckDBTypeId_1 = require("./DuckDBTypeId");
class DuckDBLogicalType {
    logical_type;
    constructor(logical_type) {
        this.logical_type = logical_type;
    }
    static create(logical_type) {
        switch (node_bindings_1.default.get_type_id(logical_type)) {
            case node_bindings_1.default.Type.DECIMAL:
                return new DuckDBDecimalLogicalType(logical_type);
            case node_bindings_1.default.Type.ENUM:
                return new DuckDBEnumLogicalType(logical_type);
            case node_bindings_1.default.Type.LIST:
                return new DuckDBListLogicalType(logical_type);
            case node_bindings_1.default.Type.STRUCT:
                return new DuckDBStructLogicalType(logical_type);
            case node_bindings_1.default.Type.MAP:
                return new DuckDBMapLogicalType(logical_type);
            case node_bindings_1.default.Type.ARRAY:
                return new DuckDBArrayLogicalType(logical_type);
            case node_bindings_1.default.Type.UNION:
                return new DuckDBUnionLogicalType(logical_type);
            default:
                return new DuckDBLogicalType(logical_type);
        }
    }
    static createDecimal(width, scale) {
        return new DuckDBDecimalLogicalType(node_bindings_1.default.create_decimal_type(width, scale));
    }
    static createEnum(member_names) {
        return new DuckDBEnumLogicalType(node_bindings_1.default.create_enum_type(member_names));
    }
    static createList(valueType) {
        return new DuckDBListLogicalType(node_bindings_1.default.create_list_type(valueType.logical_type));
    }
    static createStruct(entryNames, entryLogicalTypes) {
        const length = entryNames.length;
        if (length !== entryLogicalTypes.length) {
            throw new Error(`Could not create struct: \
        entryNames length (${entryNames.length}) does not match entryLogicalTypes length (${entryLogicalTypes.length})`);
        }
        const member_types = [];
        const member_names = [];
        for (let i = 0; i < length; i++) {
            member_types.push(entryLogicalTypes[i].logical_type);
            member_names.push(entryNames[i]);
        }
        return new DuckDBStructLogicalType(node_bindings_1.default.create_struct_type(member_types, member_names));
    }
    static createMap(keyType, valueType) {
        return new DuckDBMapLogicalType(node_bindings_1.default.create_map_type(keyType.logical_type, valueType.logical_type));
    }
    static createArray(valueType, length) {
        return new DuckDBArrayLogicalType(node_bindings_1.default.create_array_type(valueType.logical_type, length));
    }
    static createUnion(memberTags, memberLogicalTypes) {
        const length = memberTags.length;
        if (length !== memberLogicalTypes.length) {
            throw new Error(`Could not create union: \
        memberTags length (${memberTags.length}) does not match memberLogicalTypes length (${memberLogicalTypes.length})`);
        }
        const member_types = [];
        const member_names = [];
        for (let i = 0; i < length; i++) {
            member_types.push(memberLogicalTypes[i].logical_type);
            member_names.push(memberTags[i]);
        }
        return new DuckDBUnionLogicalType(node_bindings_1.default.create_union_type(member_types, member_names));
    }
    get typeId() {
        return node_bindings_1.default.get_type_id(this.logical_type);
    }
    get alias() {
        return node_bindings_1.default.logical_type_get_alias(this.logical_type) || undefined;
    }
    set alias(newAlias) {
        node_bindings_1.default.logical_type_set_alias(this.logical_type, newAlias || '');
    }
    asType() {
        const alias = this.alias;
        switch (this.typeId) {
            case DuckDBTypeId_1.DuckDBTypeId.BOOLEAN:
                return DuckDBType_1.DuckDBBooleanType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.TINYINT:
                return DuckDBType_1.DuckDBTinyIntType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.SMALLINT:
                return DuckDBType_1.DuckDBSmallIntType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.INTEGER:
                return DuckDBType_1.DuckDBIntegerType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.BIGINT:
                return DuckDBType_1.DuckDBBigIntType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.UTINYINT:
                return DuckDBType_1.DuckDBUTinyIntType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.USMALLINT:
                return DuckDBType_1.DuckDBUSmallIntType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.UINTEGER:
                return DuckDBType_1.DuckDBUIntegerType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.UBIGINT:
                return DuckDBType_1.DuckDBUBigIntType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.FLOAT:
                return DuckDBType_1.DuckDBFloatType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.DOUBLE:
                return DuckDBType_1.DuckDBDoubleType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP:
                return DuckDBType_1.DuckDBTimestampType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.DATE:
                return DuckDBType_1.DuckDBDateType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.TIME:
                return DuckDBType_1.DuckDBTimeType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.INTERVAL:
                return DuckDBType_1.DuckDBIntervalType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.HUGEINT:
                return DuckDBType_1.DuckDBHugeIntType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.UHUGEINT:
                return DuckDBType_1.DuckDBUHugeIntType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.VARCHAR:
                return DuckDBType_1.DuckDBVarCharType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.BLOB:
                return DuckDBType_1.DuckDBBlobType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.DECIMAL:
                throw new Error('Expected override');
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_S:
                return DuckDBType_1.DuckDBTimestampSecondsType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_MS:
                return DuckDBType_1.DuckDBTimestampMillisecondsType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_NS:
                return DuckDBType_1.DuckDBTimestampNanosecondsType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.ENUM:
                throw new Error('Expected override');
            case DuckDBTypeId_1.DuckDBTypeId.LIST:
                throw new Error('Expected override');
            case DuckDBTypeId_1.DuckDBTypeId.STRUCT:
                throw new Error('Expected override');
            case DuckDBTypeId_1.DuckDBTypeId.MAP:
                throw new Error('Expected override');
            case DuckDBTypeId_1.DuckDBTypeId.ARRAY:
                throw new Error('Expected override');
            case DuckDBTypeId_1.DuckDBTypeId.UUID:
                return DuckDBType_1.DuckDBUUIDType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.UNION:
                throw new Error('Expected override');
            case DuckDBTypeId_1.DuckDBTypeId.BIT:
                return DuckDBType_1.DuckDBBitType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.TIME_TZ:
                return DuckDBType_1.DuckDBTimeTZType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.TIMESTAMP_TZ:
                return DuckDBType_1.DuckDBTimestampTZType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.ANY:
                return DuckDBType_1.DuckDBAnyType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.BIGNUM:
                return DuckDBType_1.DuckDBBigNumType.create(alias);
            case DuckDBTypeId_1.DuckDBTypeId.SQLNULL:
                return DuckDBType_1.DuckDBSQLNullType.create(alias);
            default:
                throw new Error(`Unexpected type id: ${this.typeId}`);
        }
    }
}
exports.DuckDBLogicalType = DuckDBLogicalType;
class DuckDBDecimalLogicalType extends DuckDBLogicalType {
    get width() {
        return node_bindings_1.default.decimal_width(this.logical_type);
    }
    get scale() {
        return node_bindings_1.default.decimal_scale(this.logical_type);
    }
    get internalTypeId() {
        return node_bindings_1.default.decimal_internal_type(this.logical_type);
    }
    asType() {
        return new DuckDBType_1.DuckDBDecimalType(this.width, this.scale, this.alias);
    }
}
exports.DuckDBDecimalLogicalType = DuckDBDecimalLogicalType;
class DuckDBEnumLogicalType extends DuckDBLogicalType {
    get valueCount() {
        return node_bindings_1.default.enum_dictionary_size(this.logical_type);
    }
    value(index) {
        return node_bindings_1.default.enum_dictionary_value(this.logical_type, index);
    }
    values() {
        const values = [];
        const count = this.valueCount;
        for (let i = 0; i < count; i++) {
            values.push(this.value(i));
        }
        return values;
    }
    get internalTypeId() {
        return node_bindings_1.default.enum_internal_type(this.logical_type);
    }
    asType() {
        return new DuckDBType_1.DuckDBEnumType(this.values(), this.internalTypeId, this.alias);
    }
}
exports.DuckDBEnumLogicalType = DuckDBEnumLogicalType;
class DuckDBListLogicalType extends DuckDBLogicalType {
    get valueType() {
        return DuckDBLogicalType.create(node_bindings_1.default.list_type_child_type(this.logical_type));
    }
    asType() {
        return new DuckDBType_1.DuckDBListType(this.valueType.asType(), this.alias);
    }
}
exports.DuckDBListLogicalType = DuckDBListLogicalType;
class DuckDBStructLogicalType extends DuckDBLogicalType {
    get entryCount() {
        return node_bindings_1.default.struct_type_child_count(this.logical_type);
    }
    entryName(index) {
        return node_bindings_1.default.struct_type_child_name(this.logical_type, index);
    }
    entryLogicalType(index) {
        return DuckDBLogicalType.create(node_bindings_1.default.struct_type_child_type(this.logical_type, index));
    }
    entryType(index) {
        return this.entryLogicalType(index).asType();
    }
    entryNames() {
        const names = [];
        const count = this.entryCount;
        for (let i = 0; i < count; i++) {
            names.push(this.entryName(i));
        }
        return names;
    }
    entryLogicalTypes() {
        const valueTypes = [];
        const count = this.entryCount;
        for (let i = 0; i < count; i++) {
            valueTypes.push(this.entryLogicalType(i));
        }
        return valueTypes;
    }
    entryTypes() {
        const valueTypes = [];
        const count = this.entryCount;
        for (let i = 0; i < count; i++) {
            valueTypes.push(this.entryType(i));
        }
        return valueTypes;
    }
    asType() {
        return new DuckDBType_1.DuckDBStructType(this.entryNames(), this.entryTypes(), this.alias);
    }
}
exports.DuckDBStructLogicalType = DuckDBStructLogicalType;
class DuckDBMapLogicalType extends DuckDBLogicalType {
    get keyType() {
        return DuckDBLogicalType.create(node_bindings_1.default.map_type_key_type(this.logical_type));
    }
    get valueType() {
        return DuckDBLogicalType.create(node_bindings_1.default.map_type_value_type(this.logical_type));
    }
    asType() {
        return new DuckDBType_1.DuckDBMapType(this.keyType.asType(), this.valueType.asType(), this.alias);
    }
}
exports.DuckDBMapLogicalType = DuckDBMapLogicalType;
class DuckDBArrayLogicalType extends DuckDBLogicalType {
    get valueType() {
        return DuckDBLogicalType.create(node_bindings_1.default.array_type_child_type(this.logical_type));
    }
    get length() {
        return node_bindings_1.default.array_type_array_size(this.logical_type);
    }
    asType() {
        return new DuckDBType_1.DuckDBArrayType(this.valueType.asType(), this.length, this.alias);
    }
}
exports.DuckDBArrayLogicalType = DuckDBArrayLogicalType;
class DuckDBUnionLogicalType extends DuckDBLogicalType {
    get memberCount() {
        return node_bindings_1.default.union_type_member_count(this.logical_type);
    }
    memberTag(index) {
        return node_bindings_1.default.union_type_member_name(this.logical_type, index);
    }
    memberLogicalType(index) {
        return DuckDBLogicalType.create(node_bindings_1.default.union_type_member_type(this.logical_type, index));
    }
    memberType(index) {
        return this.memberLogicalType(index).asType();
    }
    memberTags() {
        const tags = [];
        const count = this.memberCount;
        for (let i = 0; i < count; i++) {
            tags.push(this.memberTag(i));
        }
        return tags;
    }
    memberLogicalTypes() {
        const valueTypes = [];
        const count = this.memberCount;
        for (let i = 0; i < count; i++) {
            valueTypes.push(this.memberLogicalType(i));
        }
        return valueTypes;
    }
    memberTypes() {
        const valueTypes = [];
        const count = this.memberCount;
        for (let i = 0; i < count; i++) {
            valueTypes.push(this.memberType(i));
        }
        return valueTypes;
    }
    asType() {
        return new DuckDBType_1.DuckDBUnionType(this.memberTags(), this.memberTypes(), this.alias);
    }
}
exports.DuckDBUnionLogicalType = DuckDBUnionLogicalType;
