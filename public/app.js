class HumanMapApp {
    constructor() {
        this.network = null;
        this.currentPerson = null;
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        const searchForm = document.getElementById('searchForm');
        const clearBtn = document.getElementById('clearBtn');
        const closeTreeBtn = document.getElementById('closeTree');
        const expandTreeBtn = document.getElementById('expandTree');

        searchForm.addEventListener('submit', (e) => this.handleSearch(e));
        clearBtn.addEventListener('click', () => this.clearSearch());
        closeTreeBtn.addEventListener('click', () => this.closeFamilyTree());
        expandTreeBtn.addEventListener('click', () => this.expandFamilyTree());
    }

    async handleSearch(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const searchParams = new URLSearchParams();
        
        for (const [key, value] of formData.entries()) {
            if (value.trim()) {
                searchParams.append(key, value.trim());
            }
        }

        this.showLoading(true);
        this.hideResults();

        try {
            const response = await fetch(`/api/search?${searchParams}`);
            const data = await response.json();

            if (data.success) {
                this.displayResults(data.data);
            } else {
                this.showError('Search failed: ' + data.error);
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    displayResults(persons) {
        const resultsContainer = document.getElementById('resultsContainer');
        const searchResults = document.getElementById('searchResults');

        if (persons.length === 0) {
            resultsContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No results found.</p>';
        } else {
            resultsContainer.innerHTML = persons.map(person => this.createPersonCard(person)).join('');
        }

        searchResults.classList.remove('hidden');
    }

    createPersonCard(person) {
        const arabicName = [person.ANAME1, person.ANAME2, person.ANAME3, person.ANAME4]
            .filter(name => name && name.trim())
            .join(' ');
        
        const englishName = [person.ENAME1, person.ENAME2, person.ENAME3, person.ENAME4]
            .filter(name => name && name.trim())
            .join(' ');

        const displayName = arabicName || englishName || 'Unknown';
        const secondaryName = arabicName && englishName && arabicName !== englishName ? englishName : '';

        return `
            <div class="person-card bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3 cursor-pointer hover:bg-gray-100"
                 onclick="app.showFamilyTree('${person.NAT_NO}')">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <h4 class="font-semibold text-lg text-gray-800">${displayName}</h4>
                        ${secondaryName ? `<p class="text-gray-600 text-sm">${secondaryName}</p>` : ''}
                        <div class="mt-2 grid grid-cols-2 gap-2 text-sm text-gray-600">
                            <div><strong>National No:</strong> ${person.NAT_NO || 'N/A'}</div>
                            <div><strong>Gender:</strong> ${person.gender_desc || (person.SEX === 1 ? 'Male' : person.SEX === 2 ? 'Female' : 'N/A')}</div>
                            <div><strong>Birth Date:</strong> ${person.BIRTHDT || person.BDate || 'N/A'}</div>
                            <div><strong>Age:</strong> ${person.age || 'N/A'}</div>
                            <div><strong>Location:</strong> ${person.city || person.governorate || 'N/A'}</div>
                            <div><strong>Status:</strong> ${person.SOCSTS_Desc || 'N/A'}</div>
                        </div>
                    </div>
                    <div class="ml-4">
                        <button class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                            View Family Tree
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    async showFamilyTree(natNo) {
        this.showLoading(true);
        
        try {
            // Get person details and family tree
            const [personResponse, treeResponse] = await Promise.all([
                fetch(`/api/person/${natNo}`),
                fetch(`/api/family-tree/${natNo}?depth=3`)
            ]);

            const personData = await personResponse.json();
            const treeData = await treeResponse.json();

            if (personData.success && treeData.success) {
                this.currentPerson = personData.data;
                this.displayPersonInfo(personData.data);
                this.renderFamilyTree(treeData.data);
                document.getElementById('familyTreeSection').classList.remove('hidden');
            } else {
                this.showError('Failed to load family tree');
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    displayPersonInfo(person) {
        const infoContainer = document.getElementById('selectedPersonInfo');
        const arabicName = [person.ANAME1, person.ANAME2, person.ANAME3, person.ANAME4]
            .filter(name => name && name.trim())
            .join(' ');
        
        const englishName = [person.ENAME1, person.ENAME2, person.ENAME3, person.ENAME4]
            .filter(name => name && name.trim())
            .join(' ');

        infoContainer.innerHTML = `
            <h4 class="font-semibold text-lg mb-2">Selected Person: ${arabicName || englishName}</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div><strong>National No:</strong> ${person.NAT_NO}</div>
                <div><strong>Gender:</strong> ${person.gender_desc || (person.SEX === 1 ? 'Male' : person.SEX === 2 ? 'Female' : 'N/A')}</div>
                <div><strong>Birth Date:</strong> ${person.BIRTHDT || person.BDate || 'N/A'}</div>
                <div><strong>Age:</strong> ${person.age || 'N/A'}</div>
            </div>
        `;
    }

    renderFamilyTree(treeData) {
        const container = document.getElementById('familyTree');
        
        // Convert tree data to vis.js format
        const { nodes, edges } = this.convertTreeToVisFormat(treeData);
        
        const data = { nodes, edges };
        const options = {
            layout: {
                hierarchical: {
                    direction: 'UD',
                    sortMethod: 'directed',
                    levelSeparation: 150,
                    nodeSpacing: 200
                }
            },
            nodes: {
                shape: 'box',
                margin: 10,
                font: { size: 12, face: 'Arial' },
                borderWidth: 2,
                shadow: true
            },
            edges: {
                arrows: { to: { enabled: true, scaleFactor: 0.5 } },
                smooth: { type: 'cubicBezier', forceDirection: 'vertical' }
            },
            physics: {
                enabled: false
            }
        };

        this.network = new vis.Network(container, data, options);
        
        // Add click event
        this.network.on('click', (params) => {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                this.showPersonDetails(nodeId);
            }
        });
    }

    convertTreeToVisFormat(treeNode, nodes = [], edges = [], visited = new Set()) {
        if (!treeNode || visited.has(treeNode.person.NAT_NO)) {
            return { nodes, edges };
        }
        
        visited.add(treeNode.person.NAT_NO);
        
        // Add current person as node
        const arabicName = [treeNode.person.ANAME1, treeNode.person.ANAME2, treeNode.person.ANAME3, treeNode.person.ANAME4]
            .filter(name => name && name.trim())
            .join(' ');
        
        const englishName = [treeNode.person.ENAME1, treeNode.person.ENAME2, treeNode.person.ENAME3, treeNode.person.ENAME4]
            .filter(name => name && name.trim())
            .join(' ');

        const displayName = arabicName || englishName || 'Unknown';
        
        nodes.push({
            id: treeNode.person.NAT_NO,
            label: displayName,
            title: `${displayName}\nNational No: ${treeNode.person.NAT_NO}\nAge: ${treeNode.person.age || 'N/A'}`,
            color: {
                background: treeNode.level === 0 ? '#3B82F6' : '#E5E7EB',
                border: '#374151'
            },
            font: {
                color: treeNode.level === 0 ? 'white' : 'black'
            }
        });
        
        // Add parents
        if (treeNode.parents.father) {
            nodes.push({
                id: treeNode.parents.father.NAT_NO,
                label: this.getPersonDisplayName(treeNode.parents.father),
                title: `Father: ${this.getPersonDisplayName(treeNode.parents.father)}`,
                color: { background: '#10B981', border: '#374151' },
                font: { color: 'white' }
            });
            edges.push({
                from: treeNode.parents.father.NAT_NO,
                to: treeNode.person.NAT_NO,
                label: 'father'
            });
        }
        
        if (treeNode.parents.mother) {
            nodes.push({
                id: treeNode.parents.mother.NAT_NO,
                label: this.getPersonDisplayName(treeNode.parents.mother),
                title: `Mother: ${this.getPersonDisplayName(treeNode.parents.mother)}`,
                color: { background: '#F59E0B', border: '#374151' },
                font: { color: 'white' }
            });
            edges.push({
                from: treeNode.parents.mother.NAT_NO,
                to: treeNode.person.NAT_NO,
                label: 'mother'
            });
        }
        
        // Add children
        treeNode.children.forEach(child => {
            this.convertTreeToVisFormat(child, nodes, edges, visited);
            edges.push({
                from: treeNode.person.NAT_NO,
                to: child.person.NAT_NO,
                label: 'child'
            });
        });
        
        return { nodes, edges };
    }

    getPersonDisplayName(person) {
        const arabicName = [person.ANAME1, person.ANAME2, person.ANAME3, person.ANAME4]
            .filter(name => name && name.trim())
            .join(' ');
        
        const englishName = [person.ENAME1, person.ENAME2, person.ENAME3, person.ENAME4]
            .filter(name => name && name.trim())
            .join(' ');

        return arabicName || englishName || 'Unknown';
    }

    async expandFamilyTree() {
        if (!this.currentPerson) return;
        
        this.showLoading(true);
        try {
            const response = await fetch(`/api/family-tree/${this.currentPerson.NAT_NO}?depth=5`);
            const data = await response.json();
            
            if (data.success) {
                this.renderFamilyTree(data.data);
            }
        } catch (error) {
            this.showError('Failed to expand family tree');
        } finally {
            this.showLoading(false);
        }
    }

    closeFamilyTree() {
        document.getElementById('familyTreeSection').classList.add('hidden');
        this.currentPerson = null;
        if (this.network) {
            this.network.destroy();
            this.network = null;
        }
    }

    clearSearch() {
        document.getElementById('searchForm').reset();
        this.hideResults();
        this.closeFamilyTree();
    }

    showLoading(show) {
        const loading = document.getElementById('loading');
        if (show) {
            loading.classList.remove('hidden');
        } else {
            loading.classList.add('hidden');
        }
    }

    hideResults() {
        document.getElementById('searchResults').classList.add('hidden');
    }

    showError(message) {
        alert('Error: ' + message);
    }
}

// Initialize the app
const app = new HumanMapApp();
