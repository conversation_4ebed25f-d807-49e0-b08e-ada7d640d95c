<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Human Map - Person Search & Family Tree</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
      .family-tree-container {
        height: 600px;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
      }
      .search-results {
        max-height: 400px;
        overflow-y: auto;
      }
      .person-card {
        transition: all 0.2s ease-in-out;
      }
      .person-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-800 mb-2">Human Map</h1>
        <p class="text-gray-600">Search for people and explore family trees</p>
      </div>

      <!-- Search Section -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Search People</h2>

        <form
          id="searchForm"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4"
        >
          <div>
            <label
              for="name"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Name (Arabic/English)</label
            >
            <input
              type="text"
              id="name"
              name="name"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter name..."
            />
          </div>

          <div>
            <label
              for="natNo"
              class="block text-sm font-medium text-gray-700 mb-1"
              >National Number</label
            >
            <input
              type="text"
              id="natNo"
              name="natNo"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter national number..."
            />
          </div>

          <div>
            <label
              for="dateOfBirth"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Date of Birth</label
            >
            <input
              type="date"
              id="dateOfBirth"
              name="dateOfBirth"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label
              for="gender"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Gender</label
            >
            <select
              id="gender"
              name="gender"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </div>
        </form>

        <div class="flex gap-4">
          <button
            type="submit"
            form="searchForm"
            class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Search
          </button>
          <button
            type="button"
            id="clearBtn"
            class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Clear
          </button>
          <button
            type="button"
            id="testMassiveTreeBtn"
            class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            Test Interactive Tree (2000111865)
          </button>
        </div>
      </div>

      <!-- Loading Indicator -->
      <div id="loading" class="hidden text-center py-8">
        <div
          class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
        ></div>
        <p class="mt-2 text-gray-600">Searching...</p>
      </div>

      <!-- Search Results -->
      <div
        id="searchResults"
        class="hidden bg-white rounded-lg shadow-md p-6 mb-8"
      >
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Search Results</h3>
        <div id="resultsContainer" class="search-results"></div>
      </div>

      <!-- Family Tree Section -->
      <div
        id="familyTreeSection"
        class="hidden bg-white rounded-lg shadow-md p-6"
      >
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold text-gray-800">Family Tree</h3>
          <div class="flex gap-2">
            <button
              id="expandTree"
              class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm"
            >
              Expand Tree
            </button>
            <button
              id="closeTree"
              class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 text-sm"
            >
              Close
            </button>
          </div>
        </div>
        <div
          id="selectedPersonInfo"
          class="mb-4 p-4 bg-gray-50 rounded-md"
        ></div>
        <div id="familyTree" class="family-tree-container"></div>
      </div>
    </div>

    <script src="app.js"></script>
  </body>
</html>
